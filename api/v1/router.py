"""
API v1 路由配置

配置所有 v1 版本的 API 路由
"""

from fastapi import APIRouter

from .base import base_router
from .douyin.router import router as douyin_router
from .douyin.collection_router import router as douyin_collection_router
from .douyin.cookies_router import router as douyin_cookies_router
from .trendinsight.router import router as trendinsight_router

# 创建 v1 路由
v1_router = APIRouter()

# 注册需要权限验证的路由
v1_router.include_router(base_router, prefix="/base")

# 平台 API 接口 - 不需要权限验证，由调用方传入cookies
v1_router.include_router(douyin_router)
v1_router.include_router(douyin_collection_router, prefix="/douyin/collection")
v1_router.include_router(douyin_cookies_router)
v1_router.include_router(trendinsight_router)
