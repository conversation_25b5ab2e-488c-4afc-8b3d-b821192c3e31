"""
抖音收藏夹 API 路由

提供抖音收藏夹相关的 API 接口
"""

from fastapi import APIRouter, BackgroundTasks, Header, Path, Query

from controllers.douyin.collection_controller import DouyinCollectionController
from controllers.trendinsight.video_process_controller import video_process_controller
from rpc.douyin.schemas import (
    CollectVideoListResponse,
    SelfAwemeCollectionResponse,
)
from schemas.douyin import CollectionSyncResponse
from schemas.responses import STANDARD_RESPONSES

router = APIRouter(
    tags=["抖音收藏夹 API"],
)

# 创建控制器实例
douyin_collection_controller = DouyinCollectionController()


# 共享的参数定义
COOKIES_HEADER_REQUIRED = Header(
    ...,
    description="抖音网站的Cookie字符串，用于身份验证和访问权限",
    example="sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz",
    min_length=10,
    title="Cookies字符串",
)


@router.get(
    "/",
    response_model=SelfAwemeCollectionResponse,
    summary="获取用户收藏夹列表(RPC响应+传入cookies)",
    description="调用方传入cookies，返回RPC原响应格式的收藏夹列表",
    operation_id="get_self_aweme_collection_rpc_with_cookies",
    tags=["抖音收藏夹 API"],
)
async def get_self_aweme_collection_rpc_with_cookies(
    cursor: int = Query(0, description="游标，用于分页", example=0, title="游标"),
    count: int = Query(10, description="每页数量", example=10, ge=1, le=100, title="每页数量"),
    cookies: str = COOKIES_HEADER_REQUIRED,
) -> SelfAwemeCollectionResponse:
    """传入cookies获取用户收藏夹列表，返回RPC原响应格式"""
    return await douyin_collection_controller.get_self_aweme_collection_rpc_with_cookies(cursor, count, cookies)


@router.get(
    "/{collects_id}/videos",
    response_model=CollectVideoListResponse,
    summary="获取收藏视频列表(RPC响应+传入cookies)",
    description="调用方传入cookies，返回RPC原响应格式的收藏视频列表",
    operation_id="get_collect_video_list_rpc_with_cookies",
    tags=["抖音收藏夹 API"],
)
async def get_collect_video_list_rpc_with_cookies(
    collects_id: str = Path(..., description="收藏夹ID", example="123456789", title="收藏夹ID"),
    cursor: int = Query(0, description="游标，用于分页", example=0, title="游标"),
    count: int = Query(10, description="每页数量", example=10, ge=1, le=100, title="每页数量"),
    cookies: str = COOKIES_HEADER_REQUIRED,
) -> CollectVideoListResponse:
    """传入cookies获取收藏视频列表，返回RPC原响应格式"""
    return await douyin_collection_controller.get_collect_video_list_rpc_with_cookies(collects_id, cursor, count, cookies)


@router.post(
    "/sync/{collection_id}",
    response_model=CollectionSyncResponse,
    summary="同步收藏夹并保存到数据库",
    description="""
同步指定收藏夹内的所有视频并保存到数据库

**功能说明：**
- 从抖音获取指定收藏夹内的所有视频
- 将新视频保存到数据库
- 建立收藏夹与视频的关联关系
- 建立与 TrendInsight 的关联关系

**参数说明：**
- collection_id: 收藏夹ID（必需）
- cookies: Cookie字符串（必需）

**返回格式：**
```json
{
  "collections_synced": 1,
  "videos_synced": 25,
  "collections_filtered": 1,
  "relations_created": 20,
  "relations_existing": 5,
  "trendinsight_relations_created": 20,
  "trendinsight_relations_existing": 5,
  "aweme_ids": ["7123456789012345678", "..."],
  "video_items": [
    {
      "aweme_id": "7123456789012345678",
      "create_time": 1705314645
    }
  ],
  "errors": []
}
```

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/douyin/collection/sync/1234567890" \\
     -H "cookies: sessionid=xxx; odin_tt=yyy; passport_csrf_token=zzz"
```
    """,
    operation_id="sync_collection_with_cookies",
    tags=["抖音收藏夹 API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "同步成功",
            "model": CollectionSyncResponse,
        },
    },
)
async def sync_collection_with_cookies(
    background_tasks: BackgroundTasks,
    collection_id: str = Path(..., description="抖音收藏夹ID", example="1234567890", title="收藏夹ID"),
    cookies: str = COOKIES_HEADER_REQUIRED,
) -> CollectionSyncResponse:
    """同步收藏夹并保存到数据库"""
    sync_result = await douyin_collection_controller.sync_and_save_single_collection_with_cookies(collection_id, cookies)
    if sync_result.aweme_ids:
        background_tasks.add_task(video_process_controller.fetch_video_trend_scores, sync_result.aweme_ids)
    return sync_result