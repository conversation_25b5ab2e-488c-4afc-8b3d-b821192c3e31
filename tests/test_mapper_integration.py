"""
抖音映射器集成测试

测试整个映射器系统的集成功能，包括：
- 与现有控制器的集成
- 端到端的数据流测试
- 实际数据源的模拟测试
"""

from datetime import datetime
from unittest.mock import patch

import pytest

from controllers.douyin import VideoFetcherController
from controllers.douyin.main_controller import DouyinController
from mappers.douyin import FetcherConfig


class TestDouyinControllerIntegration:
    """测试 DouyinController 与新映射器的集成"""

    @pytest.fixture
    def controller(self):
        return DouyinController()

    @pytest.fixture
    def mock_config(self):
        return FetcherConfig(
            preferred_methods=["mobile", "jingxuan", "rpc"],
            fallback_enabled=True,
            retry_attempts=2,
            timeout=30,
        )

    async def test_get_video_fetcher_controller(self, controller):
        """测试获取视频获取器控制器"""
        fetcher = controller.get_video_fetcher_controller()
        assert isinstance(fetcher, VideoFetcherController)

    @patch("mappers.douyin.video_fetcher_controller.VideoFetcherController.fetch_video_data")
    async def test_fetch_video_data_unified_success(self, mock_fetch, controller):
        """测试统一获取方法成功"""
        from mappers.douyin.models import FetchResult

        # 模拟成功的获取结果
        mock_result = FetchResult(
            success=True,
            aweme_id="7123456789",
            data={
                "aweme_id": "7123456789",
                "title": "测试视频",
                "author_name": "测试用户",
                "like_count": 100,
            },
            source="mobile_share",
            method_used="mobile",
            fetch_time=datetime.now(),
        )
        mock_fetch.return_value = mock_result

        result = await controller.fetch_video_data_unified("7123456789")

        assert result["success"] is True
        assert result["aweme_id"] == "7123456789"
        assert result["data"]["title"] == "测试视频"
        assert result["method"] == "mobile"
        assert result["source"] == "mobile_share"
        assert "fetch_time" in result

    @patch("mappers.douyin.video_fetcher_controller.VideoFetcherController.fetch_video_data")
    async def test_fetch_video_data_unified_failure(self, mock_fetch, controller):
        """测试统一获取方法失败"""
        from mappers.douyin.exceptions import NetworkException
        from mappers.douyin.models import FetchResult

        # 模拟失败的获取结果
        mock_result = FetchResult(
            success=False,
            aweme_id="7123456789",
            errors=[NetworkException("网络连接失败")],
        )
        mock_fetch.return_value = mock_result

        result = await controller.fetch_video_data_unified("7123456789")

        assert result["success"] is False
        assert result["aweme_id"] == "7123456789"
        assert result["errors"] is not None
        assert len(result["errors"]) > 0

    @patch("mappers.douyin.video_fetcher_controller.VideoFetcherController.batch_fetch")
    async def test_batch_fetch_video_data_unified(self, mock_batch_fetch, controller):
        """测试统一批量获取方法"""
        from mappers.douyin.models import BatchFetchResult, FetchResult

        # 模拟批量获取结果
        mock_results = [
            FetchResult(
                success=True,
                aweme_id="123",
                data={"aweme_id": "123", "title": "视频1"},
                source="mobile_share",
                method_used="mobile",
            ),
            FetchResult(
                success=True,
                aweme_id="456",
                data={"aweme_id": "456", "title": "视频2"},
                source="jingxuan_page",
                method_used="jingxuan",
            ),
            FetchResult(
                success=False,
                aweme_id="789",
                errors=[NetworkException("网络错误")],
            ),
        ]

        mock_batch_result = BatchFetchResult(results=mock_results)
        mock_batch_fetch.return_value = mock_batch_result

        aweme_ids = ["123", "456", "789"]
        results = await controller.batch_fetch_video_data_unified(aweme_ids)

        assert len(results) == 3
        assert results[0]["success"] is True
        assert results[0]["data"]["title"] == "视频1"
        assert results[1]["success"] is True
        assert results[1]["data"]["title"] == "视频2"
        assert results[2]["success"] is False

    async def test_custom_config_usage(self, controller):
        """测试自定义配置的使用"""
        custom_config = FetcherConfig(
            preferred_methods=["rpc"],
            fallback_enabled=False,
            retry_attempts=1,
            timeout=60,
        )

        with patch.object(controller.video_fetcher_controller, "fetch_video_data") as mock_fetch:
            from mappers.douyin.models import FetchResult

            mock_result = FetchResult(
                success=True,
                aweme_id="123",
                data={"aweme_id": "123"},
                method_used="rpc",
            )
            mock_fetch.return_value = mock_result

            result = await controller.fetch_video_data_unified("123", config=custom_config)

            # 验证传递的配置
            mock_fetch.assert_called_once()
            call_args = mock_fetch.call_args
            passed_config = call_args[0][1]  # 第二个参数是config

            assert passed_config.preferred_methods == ["rpc"]
            assert passed_config.fallback_enabled is False
            assert passed_config.retry_attempts == 1
            assert passed_config.timeout == 60

    async def test_backward_compatibility(self, controller):
        """测试向后兼容性 - 原有方法仍然可用"""
        # 测试原有的获取方法仍然存在且可调用
        assert hasattr(controller, "fetch_jingxuan_data")
        assert hasattr(controller, "fetch_mobile_data")
        assert hasattr(controller, "fetch_rpc_data")
        assert hasattr(controller, "fetch_video_data_auto")
        assert hasattr(controller, "batch_fetch_video_data")

        # 测试可以获取底层控制器
        html_controller = controller.get_html_controller()
        video_controller = controller.get_video_controller()
        fetcher_controller = controller.get_video_fetcher_controller()

        assert html_controller is not None
        assert video_controller is not None
        assert fetcher_controller is not None


class TestEndToEndDataFlow:
    """端到端数据流测试"""

    @pytest.fixture
    def controller(self):
        return DouyinController()

    @patch("controllers.douyin.html_controller.DouyinHTMLController.fetch_mobile_share_video_data")
    async def test_mobile_data_flow(self, mock_html_fetch, controller):
        """测试移动端数据流"""
        # 模拟 HTML 控制器返回的数据
        mock_html_data = {
            "success": True,
            "data": {
                "aweme_detail": {
                    "aweme_id": "7123456789",
                    "desc": "测试视频",
                    "create_time": 1640995200,
                    "author": {"nickname": "测试用户", "unique_id": "test_user"},
                    "video": {
                        "play_addr": {"url_list": ["https://example.com/video.mp4"]},
                        "cover": {"url_list": ["https://example.com/cover.jpg"]},
                        "duration": 15000,
                    },
                    "statistics": {"digg_count": 100, "comment_count": 50, "share_count": 25},
                }
            },
            "source": "mobile_share",
        }
        mock_html_fetch.return_value = mock_html_data

        config = FetcherConfig(preferred_methods=["mobile"])
        result = await controller.fetch_video_data_unified("7123456789", config=config)

        assert result["success"] is True
        assert result["data"]["aweme_id"] == "7123456789"
        assert result["data"]["title"] == "测试视频"
        assert result["data"]["author_name"] == "测试用户"
        assert result["method"] == "mobile"

    @patch("controllers.douyin.html_controller.DouyinHTMLController.fetch_jingxuan_video_data")
    async def test_jingxuan_data_flow(self, mock_html_fetch, controller):
        """测试精选页面数据流"""
        # 模拟精选页面数据
        mock_jingxuan_data = {
            "success": True,
            "data": {
                "aweme_id": "7123456789",
                "desc": "精选测试视频",
                "create_time": 1640995200,
                "author": {"nickname": "精选用户", "sec_uid": "test_sec_uid"},
                "video": {
                    "bit_rate": [
                        {"play_addr": {"url_list": ["https://example.com/h264.mp4"]}, "gear_name": "adapt_540_1"}
                    ],
                    "cover": {"url_list": ["https://example.com/cover.jpg"]},
                    "duration": 20000,
                },
                "statistics": {"digg_count": 200, "comment_count": 100},
            },
            "source": "jingxuan_page",
        }
        mock_html_fetch.return_value = mock_jingxuan_data

        config = FetcherConfig(preferred_methods=["jingxuan"])
        result = await controller.fetch_video_data_unified("7123456789", config=config)

        assert result["success"] is True
        assert result["data"]["aweme_id"] == "7123456789"
        assert result["data"]["title"] == "精选测试视频"
        assert result["data"]["author_name"] == "精选用户"
        assert result["method"] == "jingxuan"

    @patch("controllers.douyin.controller.DouyinController.get_video_detail")
    async def test_rpc_data_flow(self, mock_rpc_fetch, controller):
        """测试RPC数据流"""
        # 模拟RPC响应数据
        mock_rpc_data = {
            "aweme_detail": {
                "aweme_id": "7123456789",
                "desc": "RPC测试视频",
                "create_time": 1640995200,
                "author": {"nickname": "RPC用户", "unique_id": "rpc_user"},
                "video": {
                    "play_addr": {"url_list": ["https://api.example.com/video.mp4"]},
                    "cover": {"url_list": ["https://api.example.com/cover.jpg"]},
                    "duration": 30000,
                },
                "statistics": {"digg_count": 300, "comment_count": 150, "share_count": 75},
            }
        }
        mock_rpc_fetch.return_value = mock_rpc_data

        config = FetcherConfig(preferred_methods=["rpc"])
        result = await controller.fetch_video_data_unified("7123456789", config=config)

        assert result["success"] is True
        assert result["data"]["aweme_id"] == "7123456789"
        assert result["data"]["title"] == "RPC测试视频"
        assert result["data"]["author_name"] == "RPC用户"
        assert result["method"] == "rpc"


class TestErrorHandlingIntegration:
    """错误处理集成测试"""

    @pytest.fixture
    def controller(self):
        return DouyinController()

    @patch("controllers.douyin.html_controller.DouyinHTMLController.fetch_mobile_share_video_data")
    @patch("controllers.douyin.html_controller.DouyinHTMLController.fetch_jingxuan_video_data")
    async def test_fallback_on_network_error(self, mock_jingxuan, mock_mobile, controller):
        """测试网络错误时的回退机制"""
        # 模拟移动端网络错误
        mock_mobile.side_effect = Exception("网络连接失败")

        # 模拟精选页面成功
        mock_jingxuan.return_value = {
            "success": True,
            "data": {
                "aweme_id": "7123456789",
                "desc": "回退成功",
                "author": {"nickname": "回退用户"},
                "video": {"duration": 15000},
                "statistics": {"digg_count": 50},
            },
            "source": "jingxuan_page",
        }

        config = FetcherConfig(preferred_methods=["mobile", "jingxuan"], fallback_enabled=True)

        result = await controller.fetch_video_data_unified("7123456789", config=config)

        assert result["success"] is True
        assert result["method"] == "jingxuan"
        assert result["data"]["title"] == "回退成功"

    async def test_configuration_error_handling(self, controller):
        """测试配置错误处理"""
        # 测试无效配置
        with pytest.raises(ValueError):
            invalid_config = FetcherConfig(preferred_methods=["invalid_method"])
            await controller.fetch_video_data_unified("123", config=invalid_config)

    @patch("mapper.douyin.video_fetcher_controller.VideoFetcherController.fetch_video_data")
    async def test_exception_in_unified_fetcher(self, mock_fetch, controller):
        """测试统一获取器中的异常处理"""
        # 模拟统一获取器抛出异常
        mock_fetch.side_effect = Exception("内部错误")

        result = await controller.fetch_video_data_unified("123")

        assert result["success"] is False
        assert "内部错误" in result["error"]
        assert result["method"] == "unified_fetcher"
