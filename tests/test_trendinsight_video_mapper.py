"""
TrendInsight Video Mapper 测试

测试视频数据映射功能
"""

from datetime import datetime
from unittest.mock import AsyncMock, Mock

import pytest

from mappers.trendinsight.video_mapper import TrendInsightVideoMapper


class TestTrendInsightVideoMapper:
    """TrendInsight 视频映射器测试类"""

    def test_parse_timestamp_with_datetime_string(self):
        """测试解析时间字符串格式的时间戳"""
        timestamp_str = "2024-01-15 10:30:45"
        expected = int(datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S").timestamp())

        result = TrendInsightVideoMapper._parse_timestamp(timestamp_str)
        assert result == expected

    def test_parse_timestamp_with_numeric_string(self):
        """测试解析数字字符串格式的时间戳"""
        timestamp_str = "1705314645"
        expected = 1705314645

        result = TrendInsightVideoMapper._parse_timestamp(timestamp_str)
        assert result == expected

    def test_parse_timestamp_with_none(self):
        """测试解析 None 值"""
        result = TrendInsightVideoMapper._parse_timestamp(None)
        assert result == 0

    def test_parse_to_datetime_with_datetime_string(self):
        """测试解析时间字符串格式为 datetime 对象"""
        timestamp_str = "2024-01-15 10:30:45"
        expected = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

        result = TrendInsightVideoMapper._parse_to_datetime(timestamp_str)
        assert result == expected

    def test_parse_to_datetime_with_numeric_string(self):
        """测试解析数字字符串格式为 datetime 对象"""
        timestamp_str = "1705314645"
        expected = datetime.fromtimestamp(1705314645)

        result = TrendInsightVideoMapper._parse_to_datetime(timestamp_str)
        assert result == expected

    def test_parse_to_datetime_with_none(self):
        """测试解析 None 值为 datetime 对象"""
        result = TrendInsightVideoMapper._parse_to_datetime(None)
        assert isinstance(result, datetime)

    def test_parse_timestamp_with_invalid_string(self):
        """测试解析无效字符串"""
        result = TrendInsightVideoMapper._parse_timestamp("invalid_timestamp")
        assert result == 0

    def test_video_to_douyin_aweme_data_success(self):
        """测试成功转换视频数据"""
        # 创建模拟的视频对象，确保所有需要的字段都有合适的默认值
        mock_video = Mock()
        mock_video.item_id = "7123456789012345678"
        mock_video.title = "测试视频标题"
        mock_video.desc = "测试视频描述"
        mock_video.create_time = "2024-01-15 10:30:45"
        mock_video.author_user_id = "user123"
        mock_video.author_nickname = "测试用户"
        mock_video.liked_count = "1000"
        mock_video.comment_count = "50"
        mock_video.share_count = "25"
        mock_video.collected_count = "10"

        # 设置默认值以避免 Mock 对象导致的 Pydantic 验证错误
        mock_video.sec_uid = ""
        mock_video.short_user_id = ""
        mock_video.user_unique_id = ""
        mock_video.avatar = ""
        mock_video.user_signature = ""
        mock_video.ip_location = ""
        mock_video.aweme_type = "video"
        mock_video.video_url = ""
        mock_video.cover = ""
        mock_video.duration = "0"
        mock_video.play_count = "0"
        mock_video.download_count = "0"
        mock_video.forward_count = "0"
        mock_video.whatsapp_share_count = "0"
        mock_video.music_title = ""
        mock_video.music_author = ""
        mock_video.author_avatar = ""
        mock_video.author_signature = ""
        mock_video.author_follower_count = "0"
        mock_video.author_following_count = "0"
        mock_video.author_total_favorited = "0"
        mock_video.digg_count = "0"
        mock_video.collect_count = "10"
        mock_video.aweme_url = ""
        mock_video.cover_url = ""
        mock_video.video_download_url = ""

        source_keyword = "测试关键词"

        result = TrendInsightVideoMapper.video_to_douyin_aweme_data(mock_video, source_keyword)

        assert result.aweme_id == "7123456789012345678"
        assert result.title == "测试视频标题"
        assert result.desc == "测试视频描述"
        assert result.source_keyword == "测试关键词"
        assert result.user_id == "user123"
        assert result.nickname == "测试用户"

    def test_video_to_douyin_aweme_data_missing_item_id(self):
        """测试缺少 item_id 的情况"""
        mock_video = Mock()
        # 不设置 item_id 或设置为空
        mock_video.item_id = None

        with pytest.raises(AttributeError, match="视频对象缺少必要的 item_id 字段"):
            TrendInsightVideoMapper.video_to_douyin_aweme_data(mock_video, "测试")

    def test_videos_to_douyin_aweme_data_list_success(self):
        """测试批量转换视频数据"""
        # 创建模拟的视频对象列表
        mock_videos = []

        def create_mock_video_with_defaults(item_id, title):
            """创建带有所有必要字段默认值的模拟视频对象"""
            video = Mock()
            video.item_id = item_id
            video.title = title
            video.desc = f"{title} 描述"
            video.create_time = "2024-01-15 10:30:45"
            video.author_user_id = "user123"
            video.author_nickname = "测试用户"
            video.aweme_type = "video"
            video.video_url = ""
            video.cover = ""
            video.duration = "0"
            video.play_count = "0"
            video.download_count = "0"
            video.forward_count = "0"
            video.whatsapp_share_count = "0"
            video.music_title = ""
            video.music_author = ""
            video.author_avatar = ""
            video.author_signature = ""
            video.author_follower_count = "0"
            video.author_following_count = "0"
            video.author_total_favorited = "0"
            video.digg_count = "0"
            video.comment_count = "50"
            video.share_count = "25"
            video.collect_count = "10"
            video.liked_count = "100"
            video.collected_count = "10"
            video.sec_uid = ""
            video.short_user_id = ""
            video.user_unique_id = ""
            video.avatar = ""
            video.user_signature = ""
            video.ip_location = ""
            video.aweme_url = ""
            video.cover_url = ""
            video.video_download_url = ""
            return video

        # 有效视频1
        video1 = create_mock_video_with_defaults("7123456789012345678", "视频1")
        mock_videos.append(video1)

        # 有效视频2
        video2 = create_mock_video_with_defaults("7123456789012345679", "视频2")
        mock_videos.append(video2)

        # 无效视频（没有 item_id）
        video3 = Mock()
        video3.item_id = None
        mock_videos.append(video3)

        source_keyword = "测试关键词"

        video_items, video_ids = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(mock_videos, source_keyword)

        # 应该只有2个有效视频被转换
        assert len(video_items) == 2
        assert len(video_ids) == 2
        assert video_ids == ["7123456789012345678", "7123456789012345679"]
        assert video_items[0].aweme_id == "7123456789012345678"
        assert video_items[1].aweme_id == "7123456789012345679"

    def test_video_to_video_item_success(self):
        """测试成功转换为简化的 VideoItem"""
        # 创建模拟的视频对象
        mock_video = Mock()
        mock_video.item_id = "7123456789012345678"
        mock_video.create_time = "2024-01-15 10:30:45"

        result = TrendInsightVideoMapper.video_to_video_item(mock_video)

        assert result is not None
        assert result.aweme_id == "7123456789012345678"
        # 计算正确的时间戳（考虑时区差异）
        expected_timestamp = int(datetime.strptime("2024-01-15 10:30:45", "%Y-%m-%d %H:%M:%S").timestamp())
        assert result.publish_time == expected_timestamp

    def test_video_to_video_item_missing_item_id(self):
        """测试缺少 item_id 的情况"""
        mock_video = Mock()
        mock_video.item_id = None

        result = TrendInsightVideoMapper.video_to_video_item(mock_video)
        assert result is None

    def test_videos_to_video_items_success(self):
        """测试批量转换为简化的 VideoItem 列表"""
        mock_videos = []

        # 有效视频1
        video1 = Mock()
        video1.item_id = "7123456789012345678"
        video1.create_time = "2024-01-15 10:30:45"
        mock_videos.append(video1)

        # 有效视频2
        video2 = Mock()
        video2.item_id = "7123456789012345679"
        video2.create_time = "2024-01-15 11:30:45"
        mock_videos.append(video2)

        # 无效视频（没有 item_id）
        video3 = Mock()
        video3.item_id = None
        mock_videos.append(video3)

        video_items, video_ids = TrendInsightVideoMapper.videos_to_video_items(mock_videos)

        # 应该只有2个有效视频被转换
        assert len(video_items) == 2
        assert len(video_ids) == 2
        assert video_ids == ["7123456789012345678", "7123456789012345679"]
        assert video_items[0].aweme_id == "7123456789012345678"
        assert video_items[1].aweme_id == "7123456789012345679"

    @pytest.mark.asyncio
    async def test_separation_of_concerns_integration(self):
        """测试架构分离的集成测试"""
        from controllers.trendinsight.services import DouyinAwemeService
        
        # 创建模拟的视频对象
        mock_videos = []
        mock_video = Mock()
        mock_video.item_id = "test_separation_001"
        mock_video.title = "测试分离架构"
        mock_video.desc = "测试描述"
        mock_video.create_time = "2024-01-15 10:30:45"
        mock_video.author_user_id = "user123"
        mock_video.author_nickname = "测试用户"
        mock_video.aweme_type = "video"
        mock_video.video_url = ""
        mock_video.cover = ""
        mock_video.duration = "0"
        mock_video.play_count = "0"
        mock_video.download_count = "0"
        mock_video.forward_count = "0"
        mock_video.whatsapp_share_count = "0"
        mock_video.music_title = ""
        mock_video.music_author = ""
        mock_video.author_avatar = ""
        mock_video.author_signature = ""
        mock_video.author_follower_count = "0"
        mock_video.author_following_count = "0"
        mock_video.author_total_favorited = "0"
        mock_video.digg_count = "0"
        mock_video.comment_count = "50"
        mock_video.share_count = "25"
        mock_video.collect_count = "10"
        mock_video.liked_count = "100"
        mock_video.collected_count = "10"
        mock_video.sec_uid = ""
        mock_video.short_user_id = ""
        mock_video.user_unique_id = ""
        mock_video.avatar = ""
        mock_video.user_signature = ""
        mock_video.ip_location = ""
        mock_video.aweme_url = ""
        mock_video.cover_url = ""
        mock_video.video_download_url = ""
        mock_videos.append(mock_video)
        
        # 第一步：使用Mapper转换数据
        video_data_list, video_ids = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(
            mock_videos, "分离架构测试"
        )
        
        # 验证Mapper转换正确
        assert len(video_data_list) == 1
        assert len(video_ids) == 1
        assert video_data_list[0].aweme_id == "test_separation_001"
        assert video_data_list[0].source_keyword == "分离架构测试"
        
        # 第二步：使用Service处理数据库操作
        try:
            created, existing = await DouyinAwemeService.ensure_douyin_aweme_records(
                video_data_list=video_data_list, video_ids=video_ids
            )
            # 由于没有数据库连接，会返回默认值
            assert created == 0
            assert existing == 0
        except Exception as e:
            # 数据库连接错误是预期的，说明架构分离正确
            assert "connection" in str(e).lower() or "database" in str(e).lower()
            
        print("✅ 架构分离集成测试通过：Mapper专注数据转换，Service专注数据库操作")
