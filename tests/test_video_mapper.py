"""
TrendInsightVideoMapper 单元测试

测试数据转换逻辑
"""

import pytest
from datetime import datetime
from typing import Any

from mappers.trendinsight.video_mapper import TrendInsightVideoMapper


class MockVideo:
    """模拟的视频对象"""
    
    def __init__(self, **kwargs):
        # 设置默认值
        defaults = {
            'item_id': 'test_123',
            'title': '测试视频',
            'desc': '测试描述',
            'author_nickname': '测试用户',
            'author_user_id': 'user_123',
            'create_time': '2022-01-21 12:00:00',
            'liked_count': '100',
            'comment_count': '50',
            'share_count': '25',
            'collected_count': '10',
            'aweme_type': 'video'
        }
        
        # 更新传入的值
        defaults.update(kwargs)
        
        # 设置属性
        for key, value in defaults.items():
            setattr(self, key, value)


class TestTrendInsightVideoMapper:
    """TrendInsightVideoMapper 测试类"""

    def test_parse_timestamp_valid_datetime(self):
        """测试解析有效的日期时间字符串"""

        result = TrendInsightVideoMapper._parse_timestamp("2022-01-21 12:00:00")
        expected = int(datetime(2022, 1, 21, 12, 0, 0).timestamp())

        assert result == expected

    def test_parse_to_datetime_valid_datetime(self):
        """测试解析有效的日期时间字符串为 datetime 对象"""

        result = TrendInsightVideoMapper._parse_to_datetime("2022-01-21 12:00:00")
        expected = datetime(2022, 1, 21, 12, 0, 0)

        assert result == expected

    def test_parse_timestamp_valid_number(self):
        """测试解析有效的时间戳数字"""
        
        result = TrendInsightVideoMapper._parse_timestamp("1642771200")
        assert result == 1642771200

    def test_parse_timestamp_invalid_input(self):
        """测试解析无效输入"""

        # 无效格式
        assert TrendInsightVideoMapper._parse_timestamp("invalid") == 0

        # 空值
        assert TrendInsightVideoMapper._parse_timestamp("") == 0
        assert TrendInsightVideoMapper._parse_timestamp(None) == 0

    def test_parse_to_datetime_invalid_input(self):
        """测试解析无效输入为 datetime 对象"""

        # 无效格式应该返回当前时间（我们只检查类型）
        result = TrendInsightVideoMapper._parse_to_datetime("invalid")
        assert isinstance(result, datetime)

        # 空值应该返回当前时间（我们只检查类型）
        result = TrendInsightVideoMapper._parse_to_datetime("")
        assert isinstance(result, datetime)

        result = TrendInsightVideoMapper._parse_to_datetime(None)
        assert isinstance(result, datetime)

    def test_video_to_douyin_aweme_data_success(self):
        """测试成功转换视频对象到DouyinAwemeData"""
        
        mock_video = MockVideo(
            item_id="test_456",
            title="成功测试视频",
            desc="成功测试描述",
            author_nickname="成功测试用户",
            create_time="1642771200"
        )
        
        result = TrendInsightVideoMapper.video_to_douyin_aweme_data(
            video=mock_video, 
            source_keyword="成功测试"
        )
        
        assert result.aweme_id == "test_456"
        assert result.title == "成功测试视频"
        assert result.desc == "成功测试描述"
        assert result.nickname == "成功测试用户"
        assert result.create_time == datetime(2022, 1, 21, 21, 20)
        assert result.source_keyword == "成功测试"
        assert result.liked_count == "100"

    def test_video_to_douyin_aweme_data_missing_item_id(self):
        """测试缺少item_id的情况"""
        
        mock_video = MockVideo()
        delattr(mock_video, 'item_id')
        
        with pytest.raises(AttributeError, match="视频对象缺少必要的 item_id 字段"):
            TrendInsightVideoMapper.video_to_douyin_aweme_data(
                video=mock_video, 
                source_keyword="测试"
            )

    def test_video_to_douyin_aweme_data_empty_item_id(self):
        """测试item_id为空的情况"""
        
        mock_video = MockVideo(item_id="")
        
        with pytest.raises(AttributeError, match="视频对象缺少必要的 item_id 字段"):
            TrendInsightVideoMapper.video_to_douyin_aweme_data(
                video=mock_video, 
                source_keyword="测试"
            )

    def test_video_to_douyin_aweme_data_missing_optional_fields(self):
        """测试缺少可选字段的情况"""
        
        mock_video = MockVideo(item_id="test_789")
        # 移除一些可选字段
        delattr(mock_video, 'title')
        delattr(mock_video, 'desc')
        delattr(mock_video, 'author_nickname')
        
        result = TrendInsightVideoMapper.video_to_douyin_aweme_data(
            video=mock_video, 
            source_keyword="可选字段测试"
        )
        
        assert result.aweme_id == "test_789"
        assert result.title is None  # 可选字段，不存在时为None
        assert result.desc is None   # 可选字段，不存在时为None
        assert result.nickname is None  # 可选字段，不存在时为None
        assert result.source_keyword == "可选字段测试"

    def test_videos_to_douyin_aweme_data_list_success(self):
        """测试批量转换视频列表"""
        
        mock_videos = [
            MockVideo(item_id="batch_001", title="批量视频1"),
            MockVideo(item_id="batch_002", title="批量视频2"),
            MockVideo(item_id="batch_003", title="批量视频3")
        ]
        
        video_data_list, video_ids = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(
            videos=mock_videos,
            source_keyword="批量测试"
        )
        
        assert len(video_data_list) == 3
        assert len(video_ids) == 3
        assert video_ids == ["batch_001", "batch_002", "batch_003"]
        assert all(data.source_keyword == "批量测试" for data in video_data_list)
        assert video_data_list[0].title == "批量视频1"
        assert video_data_list[1].title == "批量视频2"

    def test_videos_to_douyin_aweme_data_list_with_invalid_items(self):
        """测试批量转换包含无效项的情况"""
        
        # 包含有效和无效的视频对象
        mock_videos = [
            MockVideo(item_id="valid_001", title="有效视频1"),
            MockVideo(item_id="", title="无效视频1"),  # 空item_id
            MockVideo(item_id="valid_002", title="有效视频2"),
        ]
        
        # 移除第二个对象的item_id属性
        delattr(mock_videos[1], 'item_id')
        mock_videos.append(MockVideo(title="无效视频2"))  # 这个也会被跳过
        delattr(mock_videos[-1], 'item_id')
        
        video_data_list, video_ids = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(
            videos=mock_videos,
            source_keyword="混合测试"
        )
        
        # 只有有效的两个视频应该被转换
        assert len(video_data_list) == 2
        assert len(video_ids) == 2
        assert video_ids == ["valid_001", "valid_002"]

    def test_video_to_video_item_success(self):
        """测试转换为VideoItem成功"""
        
        mock_video = MockVideo(
            item_id="video_item_001",
            create_time="1642771200"
        )
        
        result = TrendInsightVideoMapper.video_to_video_item(mock_video)
        
        assert result is not None
        assert result.aweme_id == "video_item_001"
        assert result.publish_time == 1642771200

    def test_video_to_video_item_missing_item_id(self):
        """测试转换VideoItem时缺少item_id"""
        
        mock_video = MockVideo()
        delattr(mock_video, 'item_id')
        
        result = TrendInsightVideoMapper.video_to_video_item(mock_video)
        assert result is None

    def test_videos_to_video_items_success(self):
        """测试批量转换为VideoItem列表"""
        
        mock_videos = [
            MockVideo(item_id="item_001"),
            MockVideo(item_id="item_002"),
            MockVideo(item_id="item_003")
        ]
        
        video_items, video_ids = TrendInsightVideoMapper.videos_to_video_items(mock_videos)
        
        assert len(video_items) == 3
        assert len(video_ids) == 3
        assert video_ids == ["item_001", "item_002", "item_003"]
        assert all(item.aweme_id in video_ids for item in video_items)

    def test_videos_to_video_items_with_invalid_items(self):
        """测试批量转换VideoItem包含无效项"""
        
        mock_videos = [
            MockVideo(item_id="valid_item_001"),
            MockVideo(item_id=""),  # 无效
            MockVideo(item_id="valid_item_002")
        ]
        
        video_items, video_ids = TrendInsightVideoMapper.videos_to_video_items(mock_videos)
        
        # 只有有效的两个应该被转换
        assert len(video_items) == 2
        assert len(video_ids) == 2
        assert video_ids == ["valid_item_001", "valid_item_002"]


class TestMapperBackwardCompatibility:
    """测试向后兼容性"""
    
    @pytest.mark.asyncio
    async def test_ensure_douyin_aweme_records_delegation(self):
        """测试委托方法的向后兼容性"""
        
        # 由于这个方法现在委托给服务层，我们需要mock服务层
        from unittest.mock import patch, AsyncMock
        
        mock_videos = [MockVideo(item_id="compat_001")]
        
        with patch('controllers.trendinsight.services.DouyinAwemeService') as mock_service:
            mock_service.ensure_douyin_aweme_records = AsyncMock(return_value=(1, 0))
            
            result = await TrendInsightVideoMapper.ensure_douyin_aweme_records(
                videos=mock_videos,
                source_keyword="兼容性测试"
            )
            
            # 验证委托调用
            assert result == (1, 0)
            mock_service.ensure_douyin_aweme_records.assert_called_once()


if __name__ == "__main__":
    # 简单的运行器
    def run_simple_tests():
        print("=== TrendInsightVideoMapper 简单测试 ===")
        
        # 测试时间戳解析
        result = TrendInsightVideoMapper._parse_timestamp("2022-01-21 12:00:00")
        print(f"✅ 时间戳解析测试: {result}")

        # 测试 datetime 解析
        result_dt = TrendInsightVideoMapper._parse_to_datetime("2022-01-21 12:00:00")
        print(f"✅ datetime 解析测试: {result_dt}")
        
        # 测试视频转换
        mock_video = MockVideo(item_id="simple_001", title="简单测试")
        video_data = TrendInsightVideoMapper.video_to_douyin_aweme_data(
            video=mock_video, source_keyword="简单测试"
        )
        print(f"✅ 视频转换测试: {video_data.aweme_id} - {video_data.title}")
        
        # 测试批量转换
        mock_videos = [
            MockVideo(item_id="batch_001", title="批量1"),
            MockVideo(item_id="batch_002", title="批量2")
        ]
        video_list, id_list = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(
            videos=mock_videos, source_keyword="批量测试"
        )
        print(f"✅ 批量转换测试: {len(video_list)} 个数据, IDs: {id_list}")
        
        print("简单测试完成")
    
    run_simple_tests()