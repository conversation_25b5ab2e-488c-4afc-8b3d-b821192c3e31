"""
DynamicProxyTransport 使用示例

本文件展示了如何使用 DynamicProxyTransport 及相关组件。
"""

import json
import logging
from typing import TYPE_CHECKING, Dict, Optional

import httpx

from models.enums import Platform

from ..providers.base import (
    AccountInfo,
    AccountProvider,
    DefaultAccountProvider,
    DefaultProxyProvider,
    DefaultRequestEnhancer,
    DynamicProxyTransport,
    ProxyProvider,
    RequestEnhancer,
)

if TYPE_CHECKING:
    pass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ExampleAccountProvider(AccountProvider):
    """
    示例账户信息提供者

    模拟从数据库或配置文件获取账户信息的过程。
    """

    def __init__(self):
        # 模拟的账户数据库
        self._accounts = {
            Platform.DOUYIN: {
                "cookies": {"sessionid": "fake_douyin_session_123"},
                "headers": {"X-Platform-User": "douyin_user"},
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
            },
            Platform.XIAOHONGSHU: {
                "cookies": {"web_session": "fake_xhs_session_456"},
                "headers": {"X-Platform-User": "xhs_user"},
                "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)",
            },
        }

    def get_account(self, platform: Platform) -> AccountInfo:
        """
        获取指定平台的账户信息

        Args:
            platform: 平台名称

        Returns:
            AccountInfo 类型的账户信息
        """
        if not platform or not isinstance(platform, str):
            raise ValueError("平台名称必须是非空字符串")

        account_data = self._accounts.get(platform)
        if not account_data:
            logger.warning(f"未找到平台 '{platform}' 的账户信息")
            return AccountInfo(cookies={}, headers={}, user_agent=None, account_model=None)

        logger.info(f"获取到平台 '{platform}' 的账户信息")
        return AccountInfo(
            cookies=account_data["cookies"].copy(),
            headers=account_data["headers"].copy(),
            user_agent=account_data["user_agent"],
            account_model=None,  # 在实际实现中，这里会是真实的 CrawlerCookiesAccount 实例
        )


class ExampleProxyProvider(ProxyProvider):
    """
    示例代理信息提供者

    模拟从代理池获取代理信息的过程。
    为了演示目的，这里返回 None 以避免连接假代理服务器。
    """

    def __init__(self, use_proxy: bool = False):
        # 模拟的代理池
        self._proxy_pool = [
            "http://proxy1.example.com:8080",
            "http://proxy2.example.com:8080",
            "http://proxy3.example.com:8080",
        ]
        self._current_index = 0
        self._use_proxy = use_proxy

    def get_proxy(self) -> Optional[Dict[str, str]]:
        """
        获取代理信息

        Returns:
            代理信息字典或 None
        """
        if not self._use_proxy:
            logger.info("代理已禁用，使用直连模式")
            return None

        if not self._proxy_pool:
            logger.warning("代理池为空")
            return None

        # 轮询获取代理
        proxy_url = self._proxy_pool[self._current_index]
        self._current_index = (self._current_index + 1) % len(self._proxy_pool)

        proxy_config = {"all://": proxy_url}
        logger.info(f"获取到代理: {proxy_url}")
        return proxy_config


class DouyinRequestEnhancer(RequestEnhancer):
    """
    抖音请求增强器

    为抖音平台的请求添加特定的参数，如 msToken、X-Bogus 等。
    """

    def enhance_request(self, request: httpx.Request) -> None:
        """
        为抖音请求添加特定参数

        Args:
            request: httpx.Request 对象
        """
        import time

        logger.info("抖音增强器：添加 msToken 和 X-Bogus...")

        # 获取当前 URL 参数
        params = dict(request.url.params)

        # 添加抖音特有的参数
        params["msToken"] = f"douyin_ms_token_{int(time.time())}"
        params["X-Bogus"] = "dfpbgj..."  # 实际项目中这里应该是真实的签名算法
        params["device_platform"] = "webapp"
        params["aid"] = "6383"

        # 更新请求的 URL
        request.url = request.url.copy_with(params=params)

        # 添加抖音特有的请求头
        request.headers["X-Platform"] = "douyin"
        request.headers["Referer"] = "https://www.douyin.com/"

        logger.debug(f"抖音请求增强完成，新 URL: {request.url}")


class XiaohongshuRequestEnhancer(RequestEnhancer):
    """
    小红书请求增强器

    为小红书平台的请求添加特定的参数和签名。
    """

    def enhance_request(self, request: httpx.Request) -> None:
        """
        为小红书请求添加特定参数

        Args:
            request: httpx.Request 对象
        """
        import hashlib
        import time

        logger.info("小红书增强器：添加签名和特定参数...")

        # 获取当前 URL 参数
        params = dict(request.url.params)

        # 添加小红书特有的参数
        timestamp = str(int(time.time()))
        params["t"] = timestamp
        params["source"] = "web"
        params["platform"] = "web"

        # 简单的签名示例（实际项目中应该使用真实的签名算法）
        sign_string = f"timestamp={timestamp}&source=web"
        params["sign"] = hashlib.md5(sign_string.encode()).hexdigest()[:16]

        # 更新请求的 URL
        request.url = request.url.copy_with(params=params)

        # 添加小红书特有的请求头
        request.headers["X-Platform"] = "xiaohongshu"
        request.headers["Origin"] = "https://www.xiaohongshu.com"
        request.headers["Referer"] = "https://www.xiaohongshu.com/"

        logger.debug(f"小红书请求增强完成，新 URL: {request.url}")


class DatabaseAccountProvider(AccountProvider):
    """
    基于数据库的账户信息提供者

    从 CrawlerCookiesAccount 模型获取真实的账户信息。
    简化后的版本不再需要账号释放功能。
    """

    async def get_account_async(self, platform: Platform) -> AccountInfo:
        """
        异步获取指定平台的账户信息

        Args:
            platform: 平台名称

        Returns:
            AccountInfo 类型的账户信息
        """
        if not platform or not isinstance(platform, str):
            raise ValueError("平台名称必须是非空字符串")

        try:
            # 从数据库查询有效的账户
            from models.system.crawler import CrawlerCookiesAccount

            account = await CrawlerCookiesAccount.filter(
                platform_name=platform, status=0, is_deleted=False  # 有效状态
            ).first()

            if not account:
                logger.warning(f"数据库中未找到平台 '{platform}' 的有效账户")
                return AccountInfo(cookies={}, headers={}, user_agent=None, account_model=None)

            # 解析 cookies 字符串
            cookies = {}
            if account.cookies:
                try:
                    # 假设 cookies 存储为 JSON 字符串
                    cookies = json.loads(account.cookies)
                except (json.JSONDecodeError, TypeError):
                    # 如果不是 JSON，尝试解析为 cookie 字符串格式
                    cookies = self._parse_cookie_string(account.cookies)

            logger.info(f"从数据库获取到账户: {account.account_name} (ID: {account.id})")

            return AccountInfo(
                cookies=cookies,
                headers={},  # 可以根据需要添加默认请求头
                user_agent=None,  # 可以根据平台设置默认 User-Agent
                account_model=account,
            )

        except Exception as e:
            logger.error(f"从数据库获取账户信息失败: {e}")
            return AccountInfo(cookies={}, headers={}, user_agent=None, account_model=None)

    def get_account(self, platform: Platform) -> AccountInfo:
        """
        同步接口（为了兼容抽象基类）

        注意：在实际使用中，建议使用异步版本 get_account_async
        """
        import asyncio

        # 在同步环境中运行异步方法
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.get_account_async(platform))
        except RuntimeError:
            # 如果没有运行中的事件循环，创建新的
            return asyncio.run(self.get_account_async(platform))

    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析 cookie 字符串为字典

        Args:
            cookie_string: cookie 字符串，格式如 "key1=value1; key2=value2"

        Returns:
            cookie 字典
        """
        cookies = {}
        if cookie_string:
            for item in cookie_string.split(";"):
                item = item.strip()
                if "=" in item:
                    key, value = item.split("=", 1)
                    cookies[key.strip()] = value.strip()
        return cookies


def example_basic_usage():
    """
    基本使用示例 (新的三步增强模式)
    """
    print("=== 基本使用示例 (三步增强模式) ===")

    # 1. 创建提供者实例
    account_provider = DefaultAccountProvider()
    proxy_provider = DefaultProxyProvider()
    request_enhancer = DefaultRequestEnhancer()  # 默认增强器，不做任何修改

    # 2. 创建动态传输层 (指定平台)
    transport = DynamicProxyTransport(
        platform=Platform.DOUYIN,
        account_provider=account_provider,
        proxy_provider=proxy_provider,
        request_enhancer=request_enhancer,
    )

    # 3. 创建 httpx 客户端
    client = httpx.Client(transport=transport)

    try:
        # 4. 发送请求 (不再需要在 extensions 中指定平台)
        response = client.get("https://httpbin.org/anything")

        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")

    except Exception as e:
        print(f"请求失败: {e}")
    finally:
        client.close()


def example_advanced_usage():
    """
    高级使用示例 (展示业务方自治的客户端模式)
    """
    print("\n=== 高级使用示例 (业务方自治模式) ===")

    # 共享的基础设施提供者
    account_provider = ExampleAccountProvider()
    proxy_provider = ExampleProxyProvider()

    # 测试不同平台的客户端
    platforms_and_enhancers = [
        (Platform.DOUYIN, DouyinRequestEnhancer()),
        (Platform.XIAOHONGSHU, XiaohongshuRequestEnhancer()),
    ]

    for platform, enhancer in platforms_and_enhancers:
        print(f"\n--- 测试平台: {platform} ---")

        # 每个平台创建自己的传输层和客户端
        transport = DynamicProxyTransport(
            platform=platform,
            account_provider=account_provider,
            proxy_provider=proxy_provider,
            request_enhancer=enhancer,
        )

        client = httpx.Client(transport=transport)

        try:
            # 发送请求
            response = client.get("https://httpbin.org/anything")

            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"最终 URL: {data['url']}")
            print(f"请求头中的 Cookie: {data['headers'].get('Cookie', 'None')}")
            print(f"请求头中的 X-Platform: {data['headers'].get('X-Platform', 'None')}")
            print(f"请求头中的 X-Platform-User: {data['headers'].get('X-Platform-User', 'None')}")
            print(f"User-Agent: {data['headers'].get('User-Agent', 'Default')}")

        except Exception as e:
            print(f"请求失败: {e}")
        finally:
            client.close()


def example_business_team_usage():
    """
    业务团队使用示例 (模拟真实的业务场景)

    展示抖音业务团队如何创建和使用自己的专用客户端
    """
    print("\n=== 业务团队使用示例 (抖音团队) ===")

    # 1. 共享的基础设施 (由基础设施团队提供)
    shared_account_provider = ExampleAccountProvider()
    shared_proxy_provider = ExampleProxyProvider()

    # 2. 抖音业务团队自己的请求增强器
    douyin_enhancer = DouyinRequestEnhancer()

    # 3. 抖音团队创建自己的传输层和客户端
    douyin_transport = DynamicProxyTransport(
        platform=Platform.DOUYIN,
        account_provider=shared_account_provider,
        proxy_provider=shared_proxy_provider,
        request_enhancer=douyin_enhancer,
    )

    # 4. 创建抖音专用的长生命周期客户端
    douyin_client = httpx.Client(transport=douyin_transport)

    try:
        print("抖音团队发送业务请求...")

        # 模拟抖音业务请求
        test_urls = [
            "https://httpbin.org/anything?original_param=test1",
            "https://httpbin.org/anything?video_id=123456",
        ]

        for url in test_urls:
            print(f"\n--- 请求: {url} ---")
            response = douyin_client.get(url)

            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"最终 URL: {data['url']}")

            # 检查是否正确添加了抖音特有的参数
            final_url = data["url"]
            if "msToken" in final_url and "X-Bogus" in final_url:
                print("✅ 抖音特有参数添加成功")
            else:
                print("❌ 抖音特有参数添加失败")

            # 检查请求头
            headers = data["headers"]
            if headers.get("X-Platform") == "douyin":
                print("✅ 抖音平台标识添加成功")
            else:
                print("❌ 抖音平台标识添加失败")

    except Exception as e:
        print(f"请求失败: {e}")
    finally:
        douyin_client.close()


async def example_database_usage():
    """
    数据库账户提供者使用示例
    """
    print("\n=== 数据库账户提供者示例 ===")

    # 1. 创建数据库账户提供者
    account_provider = DatabaseAccountProvider()
    proxy_provider = DefaultProxyProvider()
    request_enhancer = DefaultRequestEnhancer()

    # 2. 创建动态传输层
    transport = DynamicProxyTransport(
        platform=Platform.DOUYIN,
        account_provider=account_provider,
        proxy_provider=proxy_provider,
        request_enhancer=request_enhancer,
    )

    # 3. 创建 httpx 客户端
    client = httpx.Client(transport=transport)

    try:
        # 4. 发送请求，测试数据库账户
        print(f"\n--- 测试数据库账户: {Platform.DOUYIN} ---")
        try:
            response = client.get("https://httpbin.org/anything")

            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"请求头中的 Cookie: {data['headers'].get('Cookie', 'None')}")

        except Exception as e:
            print(f"请求失败: {e}")

    finally:
        client.close()


if __name__ == "__main__":
    # 运行示例
    example_basic_usage()
    example_advanced_usage()
    example_business_team_usage()

    # 注意：数据库示例需要在异步环境中运行
    # 在实际项目中，通常在 FastAPI 或其他异步框架中使用
    print("\n=== 数据库示例需要异步环境，请在实际项目中测试 ===")

    print("\n=== 所有示例运行完成 ===")
    print("\n🎉 新的三步增强模式 (平台客户端 + 请求增强器) 演示完成！")
    print("✨ 特点：")
    print("  - 业务方自治：每个平台团队管理自己的客户端")
    print("  - 职责分离：账户、代理、业务参数各司其职")
    print("  - 高度灵活：RequestEnhancer 可以自定义任何业务逻辑")
    print("  - 易于测试：每个组件都可以独立测试")
