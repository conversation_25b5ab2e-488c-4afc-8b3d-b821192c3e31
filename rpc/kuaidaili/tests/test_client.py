"""
快代理 RPC 客户端测试
"""

import asyncio
from unittest.mock import Mock, patch

import pytest

from rpc.kuaidaili import KuaidailiRpcClient
from rpc.kuaidaili.schemas import GetDpsResponse


@pytest.mark.unit
class TestKuaidailiRpcClient:
    """快代理 RPC 客户端测试类"""

    @pytest.fixture
    async def client(self):
        """创建测试客户端实例"""
        client = KuaidailiRpcClient(
            secret_id="test_secret_id", signature="test_signature", base_url="https://api.test.com"
        )
        yield client
        await client.close()

    @pytest.mark.asyncio
    async def test_successful_response(self, client):
        """测试成功响应"""
        # 模拟成功的 API 响应
        mock_response_data = {
            "code": 0,
            "msg": "success",
            "data": {
                "count": 2,
                "proxy_list": ["192.168.1.1:8080", "192.168.1.2:8081"],
                "order_left_count": 998,
                "dedup_count": 2,
            },
        }

        with patch.object(client.http_client, "get") as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            result = await client.get_dps(num=2)

            assert isinstance(result, GetDpsResponse)
            assert result.code == 0
            assert result.msg == "success"
            assert result.data.count == 2
            assert len(result.data.proxy_list) == 2
            assert result.data.proxy_list[0] == "192.168.1.1:8080"

    @pytest.mark.asyncio
    async def test_api_error_response(self, client):
        """测试 API 错误响应"""
        # 模拟 API 错误响应
        mock_response_data = {"code": 10001, "msg": "订单不存在或已过期", "data": None}

        with patch.object(client.http_client, "get") as mock_get:
            mock_response = Mock()
            mock_response.json.return_value = mock_response_data
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response

            with pytest.raises(Exception) as exc_info:
                await client.get_dps(num=1)

            assert "Kuaidaili API Error" in str(exc_info.value)
            assert "订单不存在或已过期" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_context_manager(self):
        """测试上下文管理器"""
        async with KuaidailiRpcClient(
            secret_id="test_secret_id", signature="test_signature", base_url="https://api.test.com"
        ) as client:
            assert client is not None
            assert client.http_client is not None
            assert client.secret_id == "test_secret_id"
            assert client.signature == "test_signature"
            assert client.base_url == "https://api.test.com"

        # 客户端应该已经关闭
        # 注意：这里我们无法直接验证 httpx.AsyncClient 是否已关闭
        # 但上下文管理器应该正确调用了 close() 方法

    def test_client_initialization_validation(self):
        """测试客户端初始化参数验证"""
        # 测试空的 secret_id
        with pytest.raises(ValueError, match="secret_id 不能为空"):
            KuaidailiRpcClient(secret_id="", signature="test_sig", base_url="https://api.test.com")

        # 测试空的 signature
        with pytest.raises(ValueError, match="signature 不能为空"):
            KuaidailiRpcClient(secret_id="test_id", signature="", base_url="https://api.test.com")

        # 测试空的 base_url
        with pytest.raises(ValueError, match="base_url 不能为空"):
            KuaidailiRpcClient(secret_id="test_id", signature="test_sig", base_url="")

        # 测试 None 值
        with pytest.raises(ValueError, match="secret_id 不能为空"):
            KuaidailiRpcClient(secret_id=None, signature="test_sig", base_url="https://api.test.com")

        with pytest.raises(ValueError, match="signature 不能为空"):
            KuaidailiRpcClient(secret_id="test_id", signature=None, base_url="https://api.test.com")

        with pytest.raises(ValueError, match="base_url 不能为空"):
            KuaidailiRpcClient(secret_id="test_id", signature="test_sig", base_url=None)


# 运行测试的脚本
if __name__ == "__main__":
    # 如果直接运行此文件，可以执行一些简单的测试
    print("运行快代理 RPC 客户端测试...")

    async def simple_test():
        try:
            client = KuaidailiRpcClient(
                secret_id="test_secret_id", signature="test_signature", base_url="https://api.test.com"
            )
            print("✓ 客户端创建成功")
            await client.close()
            print("✓ 客户端关闭成功")
        except Exception as e:
            print(f"✗ 测试失败: {e}")

    asyncio.run(simple_test())
    print("测试完成")
