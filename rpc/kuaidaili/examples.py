"""
快代理 RPC 客户端使用示例
"""

import asyncio

from rpc.kuaidaili.client import KuaidailiRpcClient, get_kuaidaili_client


async def example_basic_usage():
    """基础使用示例 - 使用工厂函数"""
    print("=== 基础使用示例 ===")

    # 使用工厂函数创建客户端（从配置文件读取参数）
    async with get_kuaidaili_client() as client:
        try:
            # 获取 5 个代理
            response = await client.get_dps(num=5)

            print(f"获取成功，状态码: {response.code}")
            print(f"消息: {response.msg}")
            print(f"获取到 {response.data.count} 个代理:")
            print(f"订单剩余: {response.data.order_left_count}")

            for i, proxy in enumerate(response.data.proxy_list, 1):
                print(f"  {i}. {proxy}")

        except Exception as e:
            print(f"获取代理失败: {e}")


async def example_with_parameters():
    """带参数的使用示例"""
    print("\n=== 带参数的使用示例 ===")

    async with get_kuaidaili_client() as client:
        try:
            # 获取 3 个 HTTPS 代理
            response = await client.get_dps(num=3, pt=2, area="北京")  # HTTPS  # 指定地区

            print(f"获取成功，状态码: {response.code}")
            print(f"消息: {response.msg}")
            print(f"获取到 {response.data.count} 个北京地区的 HTTPS 代理:")
            print(f"订单剩余: {response.data.order_left_count}")

            for i, proxy in enumerate(response.data.proxy_list, 1):
                print(f"  {i}. {proxy}")

        except Exception as e:
            print(f"获取代理失败: {e}")


async def example_manual_client():
    """手动管理客户端示例 - 直接指定参数"""
    print("\n=== 手动管理客户端示例 ===")

    # 手动创建客户端，直接指定参数
    client = KuaidailiRpcClient(order_id="your_order_id_here", base_url="https://dps.kdlapi.com/api/getdps")

    try:
        response = await client.get_dps(num=1)
        if response.data.proxy_list:
            print(f"获取到代理: {response.data.proxy_list[0]}")
            print(f"订单剩余: {response.data.order_left_count}")
        else:
            print("未获取到代理")
    except Exception as e:
        print(f"获取代理失败: {e}")
    finally:
        await client.close()


async def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")

    try:
        # 使用无效的参数创建客户端
        client = KuaidailiRpcClient(order_id="", base_url="https://api.test.com")  # 空的 order_id 会触发 ValueError
    except ValueError as e:
        print(f"参数验证失败: {e}")

    try:
        # 使用工厂函数，但配置可能不完整
        async with get_kuaidaili_client() as client:
            response = await client.get_dps(num=1)
            print("配置正确，获取代理成功")
    except ValueError as e:
        print(f"配置错误: {e}")
    except Exception as e:
        print(f"API 调用失败: {e}")


if __name__ == "__main__":
    # 运行所有示例
    print("快代理 RPC 客户端使用示例")
    print("=" * 50)

    asyncio.run(example_basic_usage())
    asyncio.run(example_with_parameters())
    asyncio.run(example_manual_client())
    asyncio.run(example_error_handling())
