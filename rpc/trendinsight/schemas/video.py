"""
TrendInsight API 视频相关数据模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field

from .base import DarenBaseResp


class VideoSearchRequest(BaseModel):
    """视频搜索请求参数"""

    # 业务参数
    keyword: str = Field(..., description="搜索关键词")
    author_ids: Optional[List[str]] = Field(default=None, description="作者ID列表")
    category_id: str = Field(default="0", description="分类ID")
    date_type: int = Field(default=0, description="日期类型")
    label_type: int = Field(default=0, description="标签类型")
    duration_type: int = Field(default=0, description="时长类型")


class VideoInfo(BaseModel):
    """视频信息（基于实际 API 响应结构）"""

    # 基础视频信息
    item_id: Optional[str] = Field(None, alias="itemId", description="视频ID")
    title: Optional[str] = Field(None, description="视频标题")
    url: Optional[str] = Field(None, description="视频链接")
    thumbnail: Optional[str] = Field(None, description="视频缩略图URL")
    duration: Optional[int] = Field(None, description="视频时长（秒）")

    # 作者信息
    nickname: Optional[str] = Field(None, description="作者昵称")
    avatar_url: Optional[str] = Field(None, alias="avatarUrl", description="作者头像URL")
    fans: Optional[int] = Field(None, description="作者粉丝数")

    # 互动数据
    likes: Optional[str] = Field(None, description="点赞数")

    # 时间信息
    create_time: Optional[str] = Field(None, alias="createTime", description="创建时间")

    # 趋势指标
    index: Optional[str] = Field(None, description="趋势指数")
    is_low_fans_but_hot: Optional[int] = Field(None, alias="isLowFansButHot", description="是否低粉高热")
    is_high_like_rate: Optional[int] = Field(None, alias="isHighLikeRate", description="是否高点赞率")
    is_high_play_over_rate: Optional[int] = Field(None, alias="isHighPlayOverRate", description="是否高完播率")
    is_high_fans_rise_rate: Optional[int] = Field(None, alias="isHighFansRiseRate", description="是否高涨粉率")

    # 兼容性字段（保持向后兼容）
    author_name: Optional[str] = Field(None, description="作者名称（兼容字段）")
    author_id: Optional[str] = Field(None, description="作者ID（兼容字段）")
    play_count: Optional[str] = Field(None, description="播放数（兼容字段）")
    like_count: Optional[str] = Field(None, description="点赞数（兼容字段）")
    comment_count: Optional[str] = Field(None, description="评论数（兼容字段）")
    share_count: Optional[str] = Field(None, description="分享数（兼容字段）")
    cover_url: Optional[str] = Field(None, description="封面URL（兼容字段）")
    video_url: Optional[str] = Field(None, description="视频URL（兼容字段）")

    class Config:
        extra = "allow"
        populate_by_name = True  # 允许使用字段名和别名


class VideoSearchData(BaseModel):
    """视频搜索数据容器"""

    data: Optional[List[VideoInfo]] = Field(None, description="视频列表")

    class Config:
        extra = "allow"


# rpc/trendinsight/tests/mock/search_info_by_keyword.json
class VideoSearchResponse(BaseModel):
    """视频搜索响应"""

    data: Optional[VideoSearchData] = Field(None, description="视频搜索数据")
    msg: Optional[str] = Field(None, description="响应消息")
    status: Optional[int] = Field(None, description="状态码")

    @property
    def video_list(self) -> List[VideoInfo]:
        """获取视频列表的便捷属性"""
        if self.data and self.data.data:
            return self.data.data
        return []

    @property
    def video_items(self) -> List[VideoInfo]:
        """获取视频列表的便捷属性（别名，与 video_list 相同）"""
        return self.video_list

    @property
    def video_count(self) -> int:
        """获取视频数量"""
        return len(self.video_list)

    @property
    def is_success(self) -> bool:
        """判断请求是否成功"""
        return self.status == 0

    class Config:
        extra = "allow"


class VideoIndexRequest(BaseModel):
    """视频指数请求参数"""

    item_id: str = Field(..., description="视频ID")
    start_date: str = Field(..., description="开始日期，格式：YYYYMMDD")
    end_date: str = Field(..., description="结束日期，格式：YYYYMMDD")


class VideoIndexTrendItem(BaseModel):
    """视频指数趋势项"""

    datetime: str = Field(..., description="日期时间，格式：YYYYMMDD")
    value: str = Field(..., description="指数值")

    class Config:
        extra = "allow"


class VideoIndexData(BaseModel):
    """视频指数数据"""

    trend: List[VideoIndexTrendItem] = Field(..., description="趋势数据列表")
    avg: str = Field(..., description="平均值")
    BaseResp: DarenBaseResp = Field(..., description="基础响应信息")

    class Config:
        extra = "allow"


class VideoIndexResponse(BaseModel):
    """视频指数响应"""

    data: VideoIndexData = Field(..., description="响应数据")
    msg: str = Field(default="", description="响应消息")
    status: int = Field(..., description="响应状态码")

    @property
    def is_success(self) -> bool:
        """判断请求是否成功"""
        return self.status == 0

    @property
    def trend_data(self) -> List[VideoIndexTrendItem]:
        """获取趋势数据列表"""
        return self.data.trend

    @property
    def average_value(self) -> str:
        """获取平均值"""
        return self.data.avg

    @property
    def trend_count(self) -> int:
        """获取趋势数据点数量"""
        return len(self.data.trend)

    class Config:
        extra = "allow"
