"""
TrendInsight 新架构高级使用示例

展示高级功能，包括自定义增强器、批量操作、性能优化等
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict

from loguru import logger

from rpc.trendinsight import (
    AsyncTrendInsightAPI,
    DarenSearchRequest,
    TrendInsightAPI,
    TrendInsightClientManager,
    TrendInsightConfig,
    create_async_trendinsight_client,
    create_trendinsight_client,
)


class CustomTrendInsightAPI(TrendInsightAPI):
    """自定义API类示例"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.request_count = 0
        self.start_time = time.time()

    def _handle_response(self, response):
        """重写响应处理，添加统计功能"""
        self.request_count += 1
        logger.info(f"请求 #{self.request_count} 完成，状态码: {response.status_code}")
        return super()._handle_response(response)

    def get_stats(self) -> Dict[str, Any]:
        """获取API统计信息"""
        elapsed = time.time() - self.start_time
        return {
            "request_count": self.request_count,
            "elapsed_time": elapsed,
            "requests_per_second": self.request_count / elapsed if elapsed > 0 else 0,
        }


def example_custom_api_class():
    """自定义API类示例"""
    print("=== 自定义API类示例 ===")

    config = TrendInsightConfig()
    client = create_trendinsight_client(config=config, auto_generate_params=True)

    # 使用自定义API类
    api = CustomTrendInsightAPI(client=client, config=config)

    try:
        # 执行一些请求
        print("执行多个请求...")

        # 测试连接
        api.pong()

        # 查询用户信息
        try:
            api.query_user_self_info()
        except Exception as e:
            logger.warning(f"用户信息查询失败: {e}")

        # 搜索达人
        try:
            request = DarenSearchRequest(keyword="测试", total=5)
            api.query_daren_sug_great_user_list(request)
        except Exception as e:
            logger.warning(f"达人搜索失败: {e}")

        # 获取统计信息
        stats = api.get_stats()
        print(f"API统计信息: {stats}")

    finally:
        client.close()


def example_batch_operations():
    """批量操作示例"""
    print("\n=== 批量操作示例 ===")

    # 要搜索的关键词列表
    keywords = ["美食", "科技", "旅游", "时尚", "健康"]

    config = TrendInsightConfig()

    def search_keyword(keyword: str) -> Dict[str, Any]:
        """搜索单个关键词"""
        client = create_trendinsight_client(config=config, auto_generate_params=True)
        api = TrendInsightAPI(client=client, config=config)

        try:
            request = DarenSearchRequest(keyword=keyword, total=3)
            result = api.query_daren_sug_great_user_list(request)

            return {
                "keyword": keyword,
                "success": True,
                "count": len(result.userlist) if result and result.userlist else 0,
                "users": [user.user_name for user in (result.userlist or [])[:3]],
            }
        except Exception as e:
            return {"keyword": keyword, "success": False, "error": str(e)}
        finally:
            client.close()

    # 使用线程池进行并发搜索
    print(f"并发搜索 {len(keywords)} 个关键词...")
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交任务
        future_to_keyword = {executor.submit(search_keyword, keyword): keyword for keyword in keywords}

        # 收集结果
        results = []
        for future in as_completed(future_to_keyword):
            result = future.result()
            results.append(result)

            if result["success"]:
                print(f"✅ {result['keyword']}: 找到 {result['count']} 个达人")
            else:
                print(f"❌ {result['keyword']}: {result['error']}")

    elapsed = time.time() - start_time
    print(f"批量操作完成，耗时: {elapsed:.2f}秒")

    # 统计结果
    successful = sum(1 for r in results if r["success"])
    total_users = sum(r.get("count", 0) for r in results if r["success"])
    print(f"成功: {successful}/{len(keywords)}, 总达人数: {total_users}")


async def example_async_batch_operations():
    """异步批量操作示例"""
    print("\n=== 异步批量操作示例 ===")

    keywords = ["音乐", "舞蹈", "游戏", "教育", "娱乐"]

    async def async_search_keyword(keyword: str) -> Dict[str, Any]:
        """异步搜索单个关键词"""
        config = TrendInsightConfig()
        client = create_async_trendinsight_client(config=config, auto_generate_params=True)
        api = AsyncTrendInsightAPI(async_client=client, config=config)

        try:
            request = DarenSearchRequest(keyword=keyword, total=3)
            result = await api.query_daren_sug_great_user_list(request)

            return {
                "keyword": keyword,
                "success": True,
                "count": len(result.userlist) if result and result.userlist else 0,
            }
        except Exception as e:
            return {"keyword": keyword, "success": False, "error": str(e)}
        finally:
            await client.aclose()

    print(f"异步并发搜索 {len(keywords)} 个关键词...")
    start_time = time.time()

    # 创建异步任务
    tasks = [async_search_keyword(keyword) for keyword in keywords]

    # 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)

    elapsed = time.time() - start_time
    print(f"异步批量操作完成，耗时: {elapsed:.2f}秒")

    # 处理结果
    for result in results:
        if isinstance(result, Exception):
            print(f"❌ 任务异常: {result}")
        elif result["success"]:
            print(f"✅ {result['keyword']}: 找到 {result['count']} 个达人")
        else:
            print(f"❌ {result['keyword']}: {result['error']}")


def example_client_manager():
    """客户端管理器示例"""
    print("\n=== 客户端管理器示例 ===")

    # 创建自定义配置
    config = TrendInsightConfig()
    config.timeout = 60.0

    # 创建客户端管理器
    manager = TrendInsightClientManager(config)

    try:
        print("使用客户端管理器...")

        # 获取同步客户端（单例）
        sync_client1 = manager.get_sync_client()
        sync_client2 = manager.get_sync_client()

        print(f"同步客户端单例验证: {sync_client1 is sync_client2}")

        # 获取异步客户端（单例）
        async_client1 = manager.get_async_client()
        async_client2 = manager.get_async_client()

        print(f"异步客户端单例验证: {async_client1 is async_client2}")

        # 使用客户端
        api = TrendInsightAPI(client=sync_client1, config=config)
        is_connected = api.pong()
        print(f"管理器客户端连接测试: {'✅ 正常' if is_connected else '❌ 异常'}")

    finally:
        # 清理资源
        manager.close_sync_client()
        asyncio.run(manager.close_async_client())
        print("客户端管理器资源已清理")


def example_performance_monitoring():
    """性能监控示例"""
    print("\n=== 性能监控示例 ===")

    config = TrendInsightConfig()
    client = create_trendinsight_client(config=config, auto_generate_params=True)
    api = TrendInsightAPI(client=client, config=config)

    try:
        # 获取增强器统计信息
        enhancer = client._transport.request_enhancer

        print("执行请求前的增强器统计:")
        stats_before = enhancer.get_stats()
        print(f"  验证参数已生成: {stats_before.get('verify_params_generated', False)}")
        print(f"  自动生成参数: {stats_before.get('auto_generate_params', False)}")

        if "sign_client_stats" in stats_before:
            sign_stats = stats_before["sign_client_stats"]
            print("  签名客户端统计:")
            print(f"    总请求数: {sign_stats.get('total_requests', 0)}")
            print(f"    成功请求数: {sign_stats.get('successful_requests', 0)}")
            print(f"    缓存命中数: {sign_stats.get('cache_hits', 0)}")

        # 执行一些请求
        print("\n执行测试请求...")
        start_time = time.time()

        # 测试连接
        api.pong()

        # 尝试查询用户信息
        try:
            api.query_user_self_info()
        except Exception as e:
            logger.warning(f"用户信息查询失败: {e}")

        elapsed = time.time() - start_time
        print(f"请求执行时间: {elapsed:.2f}秒")

        # 获取执行后的统计信息
        print("\n执行请求后的增强器统计:")
        stats_after = enhancer.get_stats()

        if "sign_client_stats" in stats_after:
            sign_stats = stats_after["sign_client_stats"]
            print("  签名客户端统计:")
            print(f"    总请求数: {sign_stats.get('total_requests', 0)}")
            print(f"    成功请求数: {sign_stats.get('successful_requests', 0)}")
            print(f"    失败请求数: {sign_stats.get('failed_requests', 0)}")
            print(f"    缓存命中数: {sign_stats.get('cache_hits', 0)}")
            print(f"    缓存未命中数: {sign_stats.get('cache_misses', 0)}")

        # 清理缓存
        print("\n清理增强器缓存...")
        enhancer.clear_cache()
        print("缓存已清理")

    finally:
        client.close()


def example_error_recovery():
    """错误恢复示例"""
    print("\n=== 错误恢复示例 ===")

    from rpc.trendinsight.exceptions import (
        TrendInsightAuthenticationError,
        TrendInsightRateLimitError,
    )

    config = TrendInsightConfig()
    config.max_retries = 3
    config.retry_delay = 1.0

    def robust_api_call(api_func, *args, **kwargs):
        """带重试的API调用"""
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(max_retries):
            try:
                return api_func(*args, **kwargs)

            except TrendInsightRateLimitError:
                if attempt < max_retries - 1:
                    print(f"频率限制，等待 {retry_delay} 秒后重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    raise

            except TrendInsightAuthenticationError as e:
                print(f"认证失败，无法重试: {e}")
                raise

            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"请求失败，重试... (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(retry_delay)
                else:
                    raise

    client = create_trendinsight_client(config=config, auto_generate_params=True)
    api = TrendInsightAPI(client=client, config=config)

    try:
        print("执行带错误恢复的API调用...")

        # 使用错误恢复机制
        result = robust_api_call(api.pong)
        print(f"连接测试结果: {'✅ 成功' if result else '❌ 失败'}")

        # 尝试查询用户信息
        try:
            user_info = robust_api_call(api.query_user_self_info)
            print("用户信息查询成功")
        except Exception as e:
            print(f"用户信息查询最终失败: {e}")

    finally:
        client.close()


def main():
    """主函数"""
    print("🚀 TrendInsight 新架构高级使用示例")
    print("=" * 50)

    # 自定义API类示例
    example_custom_api_class()

    # 批量操作示例
    example_batch_operations()

    # 异步批量操作示例
    print("\n运行异步批量操作示例...")
    asyncio.run(example_async_batch_operations())

    # 客户端管理器示例
    example_client_manager()

    # 性能监控示例
    example_performance_monitoring()

    # 错误恢复示例
    example_error_recovery()

    print("\n✅ 所有高级示例执行完成")
    print("\n💡 高级功能提示:")
    print("  - 使用客户端管理器可以更好地管理资源")
    print("  - 批量操作时注意控制并发数量")
    print("  - 监控性能统计信息有助于优化")
    print("  - 实现错误恢复机制提高稳定性")


if __name__ == "__main__":
    main()
