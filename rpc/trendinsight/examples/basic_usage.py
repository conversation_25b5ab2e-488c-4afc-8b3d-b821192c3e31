"""
TrendInsight 新架构基础使用示例

展示如何使用新的 TrendInsight API 客户端进行基本操作
"""

import asyncio

# 导入新架构的组件
from rpc.trendinsight import (  # 便捷函数（推荐使用）; API 类; 模型; 配置; 客户端管理器
    AsyncTrendInsightAPI,
    DarenSearchRequest,
    TrendInsightAPI,
    TrendInsightClientManager,
    TrendInsightConfig,
    pong,
    query_daren_sug_great_user_list,
    query_user_self_info,
    search_info_by_keyword,
)


def example_basic_usage():
    """基础使用示例 - 使用便捷函数"""
    print("=== TrendInsight 新架构基础使用示例 ===")

    try:
        # 1. 测试连接
        print("\n1. 测试API连接...")
        is_connected = pong()
        print(f"连接状态: {'✅ 正常' if is_connected else '❌ 异常'}")

        if not is_connected:
            print("⚠️  API连接异常，可能需要配置有效的认证信息")
            return

        # 2. 查询用户信息
        print("\n2. 查询用户信息...")
        try:
            user_info = query_user_self_info()
            if user_info and user_info.data:
                print(f"用户ID: {user_info.data.user_id}")
                print(f"用户名: {user_info.data.nickname}")
            else:
                print("用户信息为空")
        except Exception as e:
            print(f"查询用户信息失败: {e}")

        # 3. 搜索达人
        print("\n3. 搜索达人...")
        try:
            request = DarenSearchRequest(keyword="美食", total=5)
            daren_result = query_daren_sug_great_user_list(request)

            if daren_result and daren_result.userlist:
                print(f"找到 {len(daren_result.userlist)} 个达人:")
                for i, user in enumerate(daren_result.userlist[:3], 1):
                    print(f"  {i}. {user.user_name} (ID: {user.user_id})")
            else:
                print("未找到达人")
        except Exception as e:
            print(f"搜索达人失败: {e}")

        # 4. 搜索视频
        print("\n4. 搜索视频...")
        try:
            video_result = search_info_by_keyword(
                keyword="美食", date_type=1, label_type=0, duration_type=0  # 最近7天  # 全部标签  # 全部时长
            )

            if video_result and video_result.video_list:
                print(f"找到 {video_result.video_count} 个视频:")
                for i, video in enumerate(video_result.video_list[:3], 1):
                    print(f"  {i}. {video.title} - {video.author_name}")
            else:
                print("未找到视频")
        except Exception as e:
            print(f"搜索视频失败: {e}")

    except Exception as e:
        print(f"示例执行失败: {e}")


def example_api_class_usage():
    """API类使用示例"""
    print("\n=== API类使用示例 ===")

    # 创建自定义配置
    config = TrendInsightConfig()
    config.timeout = 60.0  # 设置超时时间

    # 创建自定义客户端管理器
    manager = TrendInsightClientManager()
    client = manager.create_client(config=config)

    # 创建API实例
    api = TrendInsightAPI(client=client, config=config)

    try:
        # 使用API实例
        print("使用API类进行查询...")

        # 测试连接
        is_connected = api.pong()
        print(f"API连接状态: {'✅ 正常' if is_connected else '❌ 异常'}")

        if is_connected:
            # 查询用户信息
            try:
                user_info = api.query_user_self_info()
                print(f"用户信息查询成功: {user_info.data.nickname if user_info.data else 'N/A'}")
            except Exception as e:
                print(f"用户信息查询失败: {e}")

    except Exception as e:
        print(f"API类使用失败: {e}")

    finally:
        # 关闭客户端
        manager.close_client(client)
        print("客户端已关闭")


async def example_async_usage():
    """异步使用示例"""
    print("\n=== 异步使用示例 ===")

    # 创建异步API实例
    async_api = AsyncTrendInsightAPI()

    try:
        # 异步查询用户信息
        print("异步查询用户信息...")
        user_info = await async_api.query_user_self_info()

        if user_info and user_info.data:
            print(f"异步查询成功: {user_info.data.nickname}")
        else:
            print("异步查询结果为空")

        # 异步搜索达人
        print("异步搜索达人...")
        request = DarenSearchRequest(keyword="科技", total=3)
        daren_result = await async_api.query_daren_sug_great_user_list(request)

        if daren_result and daren_result.userlist:
            print(f"异步搜索到 {len(daren_result.userlist)} 个达人")

        # 异步测试连接
        is_connected = await async_api.pong()
        print(f"异步连接测试: {'✅ 正常' if is_connected else '❌ 异常'}")

    except Exception as e:
        print(f"异步使用失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")

    from rpc.trendinsight.exceptions import (
        DataFetchError,
        TrendInsightAuthenticationError,
        TrendInsightRateLimitError,
        TrendInsightRequestError,
    )

    try:
        # 尝试查询用户信息
        user_info = query_user_self_info()
        print("查询成功")

    except TrendInsightAuthenticationError as e:
        print(f"❌ 认证失败: {e}")
        print("💡 请检查cookies配置")

    except TrendInsightRateLimitError as e:
        print(f"❌ 请求频率过高: {e}")
        print("💡 请稍后重试")

    except TrendInsightRequestError as e:
        print(f"❌ 请求错误: {e}")
        print(f"💡 状态码: {e.status_code}")

    except DataFetchError as e:
        print(f"❌ 数据获取失败: {e}")
        print("💡 可能账号被限制")

    except Exception as e:
        print(f"❌ 未知错误: {e}")


def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")

    # 创建自定义配置
    config = TrendInsightConfig()

    # 修改配置
    config.timeout = 120.0  # 2分钟超时
    config.max_retries = 5  # 最大重试5次
    config.verify_ssl = True  # 启用SSL验证
    config.use_javascript_sign = True  # 使用JavaScript签名

    print("配置信息:")
    print(f"  超时时间: {config.timeout}秒")
    print(f"  最大重试: {config.max_retries}次")
    print(f"  SSL验证: {config.verify_ssl}")
    print(f"  JS签名: {config.use_javascript_sign}")

    # 使用自定义配置创建客户端
    client = create_trendinsight_client(
        config=config,
        user_agent="Custom-TrendInsight-Client/1.0",
        auto_generate_params=True,
        timeout=90.0,  # 这个会覆盖config中的timeout
    )

    try:
        # 使用自定义客户端
        api = TrendInsightAPI(client=client, config=config)

        # 测试连接
        is_connected = api.pong()
        print(f"自定义配置客户端连接: {'✅ 正常' if is_connected else '❌ 异常'}")

    finally:
        client.close()


def main():
    """主函数"""
    print("🚀 TrendInsight 新架构使用示例")
    print("=" * 50)

    # 基础使用示例
    example_basic_usage()

    # API类使用示例
    example_api_class_usage()

    # 异步使用示例
    print("\n运行异步示例...")
    asyncio.run(example_async_usage())

    # 错误处理示例
    example_error_handling()

    # 自定义配置示例
    example_custom_config()

    print("\n✅ 所有示例执行完成")
    print("\n💡 提示:")
    print("  - 如果遇到认证错误，请配置有效的cookies")
    print("  - 如果遇到频率限制，请适当降低请求频率")
    print("  - 更多高级用法请参考文档")


if __name__ == "__main__":
    main()
