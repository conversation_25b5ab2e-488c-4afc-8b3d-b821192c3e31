"""
抖音搜索数据模型
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class SearchInfoRequest(BaseModel):
    """搜索请求参数"""

    keyword: str = Field(..., description="搜索关键词")
    offset: int = Field(0, description="分页偏移量")
    count: int = Field(20, description="每页数量")
    search_id: Optional[str] = Field(None, description="搜索ID")


class SearchInfoResponse(BaseModel):
    """搜索响应"""

    status_code: int = Field(..., description="状态码")
    data: List[Dict[str, Any]] = Field([], description="搜索结果列表")
    has_more: bool = Field(False, description="是否有更多")
    cursor: int = Field(0, description="下一个分页游标")

    class Config:
        extra = "allow"


class DiscoverSearchRequest(BaseModel):
    """发现搜索请求参数"""

    keyword: str = Field(..., description="搜索关键词")
    offset: int = Field(0, description="分页偏移量")
    count: int = Field(20, description="每页数量")
    search_channel: Optional[str] = Field(None, description="搜索频道")


class AvatarThumb(BaseModel):
    """头像缩略图"""
    
    uri: str = Field(..., description="URI")
    url_list: List[str] = Field(..., description="URL列表")
    width: int = Field(..., description="宽度")
    height: int = Field(..., description="高度")


class UserInfo(BaseModel):
    """用户信息"""
    
    uid: str = Field(..., description="用户ID")
    short_id: str = Field(..., description="短ID")
    nickname: str = Field(..., description="昵称")
    signature: str = Field("", description="个性签名")
    avatar_thumb: AvatarThumb = Field(..., description="头像缩略图")
    follow_status: int = Field(0, description="关注状态")
    follower_count: int = Field(0, description="粉丝数")
    total_favorited: int = Field(0, description="获赞总数")
    custom_verify: str = Field("", description="自定义认证")
    unique_id: str = Field(..., description="唯一ID")
    room_id: int = Field(0, description="直播间ID")
    enterprise_verify_reason: str = Field("", description="企业认证原因")
    secret: int = Field(0, description="隐私设置")
    follower_status: int = Field(0, description="粉丝状态")
    sec_uid: str = Field(..., description="安全用户ID")
    room_id_str: str = Field("0", description="直播间ID字符串")
    user_tags: List[Any] = Field([], description="用户标签")
    versatile_display: str = Field("", description="多功能显示")
    follower_count_str: str = Field("", description="粉丝数字符串")
    # 其他可选字段
    followers_detail: Optional[Any] = Field(None, description="粉丝详情")
    platform_sync_info: Optional[Any] = Field(None, description="平台同步信息")
    geofencing: Optional[Any] = Field(None, description="地理围栏")
    cover_url: Optional[str] = Field(None, description="封面URL")
    item_list: Optional[List[Any]] = Field(None, description="作品列表")
    new_story_cover: Optional[Any] = Field(None, description="新故事封面")
    type_label: Optional[Any] = Field(None, description="类型标签")
    ad_cover_url: Optional[str] = Field(None, description="广告封面URL")
    relative_users: Optional[List[Any]] = Field(None, description="相关用户")
    cha_list: Optional[List[Any]] = Field(None, description="话题列表")
    need_points: Optional[Any] = Field(None, description="需要积分")
    homepage_bottom_toast: Optional[Any] = Field(None, description="主页底部提示")
    room_data: str = Field("", description="直播间数据")
    can_set_geofencing: Optional[bool] = Field(None, description="是否可设置地理围栏")
    white_cover_url: Optional[str] = Field(None, description="白色封面URL")
    ban_user_functions: Optional[List[Any]] = Field(None, description="禁用用户功能")
    card_entries: Optional[List[Any]] = Field(None, description="卡片条目")
    display_info: Optional[Any] = Field(None, description="显示信息")
    card_entries_not_display: Optional[List[Any]] = Field(None, description="不显示的卡片条目")
    card_sort_priority: Optional[int] = Field(None, description="卡片排序优先级")
    interest_tags: Optional[List[Any]] = Field(None, description="兴趣标签")
    link_item_list: Optional[List[Any]] = Field(None, description="链接条目列表")
    user_permissions: Optional[Any] = Field(None, description="用户权限")
    offline_info_list: Optional[List[Any]] = Field(None, description="离线信息列表")
    signature_extra: Optional[Any] = Field(None, description="签名额外信息")
    personal_tag_list: Optional[List[Any]] = Field(None, description="个人标签列表")
    cf_list: Optional[List[Any]] = Field(None, description="CF列表")
    im_role_ids: Optional[List[Any]] = Field(None, description="IM角色ID")
    not_seen_item_id_list: Optional[List[Any]] = Field(None, description="未见条目ID列表")
    follower_list_secondary_information_struct: Optional[Any] = Field(None, description="粉丝列表二级信息结构")
    endorsement_info_list: Optional[List[Any]] = Field(None, description="代言信息列表")
    text_extra: Optional[Any] = Field(None, description="文本额外信息")
    contrail_list: Optional[List[Any]] = Field(None, description="轨迹列表")
    data_label_list: Optional[List[Any]] = Field(None, description="数据标签列表")
    not_seen_item_id_list_v2: Optional[List[Any]] = Field(None, description="未见条目ID列表V2")
    special_people_labels: Optional[List[Any]] = Field(None, description="特殊人群标签")
    familiar_visitor_user: Optional[Any] = Field(None, description="熟悉访客用户")
    avatar_schema_list: Optional[List[Any]] = Field(None, description="头像架构列表")
    profile_mob_params: Optional[Any] = Field(None, description="档案移动参数")
    verification_permission_ids: Optional[List[Any]] = Field(None, description="验证权限ID")
    batch_unfollow_relation_desc: Optional[str] = Field(None, description="批量取消关注关系描述")
    batch_unfollow_contain_tabs: Optional[List[Any]] = Field(None, description="批量取消关注包含标签")
    creator_tag_list: Optional[List[Any]] = Field(None, description="创作者标签列表")
    private_relation_list: Optional[List[Any]] = Field(None, description="私密关系列表")
    identity_labels: Optional[List[Any]] = Field(None, description="身份标签")


class UserSearchResult(BaseModel):
    """用户搜索结果"""
    
    user_info: UserInfo = Field(..., description="用户信息")
    position: Optional[Any] = Field(None, description="位置")
    uniqid_position: Optional[Any] = Field(None, description="唯一ID位置")
    effects: Optional[List[Any]] = Field(None, description="特效")
    musics: Optional[List[Any]] = Field(None, description="音乐")
    items: Optional[List[Any]] = Field(None, description="条目")
    mix_list: Optional[List[Any]] = Field(None, description="混合列表")
    challenges: Optional[List[Any]] = Field(None, description="挑战")
    product_info: Optional[Any] = Field(None, description="产品信息")
    product_list: Optional[List[Any]] = Field(None, description="产品列表")
    is_red_uniqueid: bool = Field(False, description="是否为红色唯一ID")
    baikes: Optional[List[Any]] = Field(None, description="百科")
    userSubLightApp: Optional[Any] = Field(None, description="用户子轻应用")
    shop_product_info: Optional[Any] = Field(None, description="商店产品信息")
    user_service_info: Optional[Any] = Field(None, description="用户服务信息")


class FilterItem(BaseModel):
    """过滤项"""
    
    title: str = Field(..., description="标题")
    value: str = Field(..., description="值")
    log_value: str = Field(..., description="日志值")


class FilterSetting(BaseModel):
    """过滤设置"""
    
    title: str = Field(..., description="标题")
    name: str = Field(..., description="名称")
    default_index: int = Field(0, description="默认索引")
    log_name: str = Field(..., description="日志名称")
    items: List[FilterItem] = Field(..., description="过滤项列表")


class GlobalDoodleConfig(BaseModel):
    """全局涂鸦配置"""
    
    keyword: str = Field(..., description="关键词")
    filter_show_dot: int = Field(..., description="过滤显示点")
    filter_settings: List[FilterSetting] = Field(..., description="过滤设置")


class LogPb(BaseModel):
    """日志PB"""
    
    impr_id: str = Field(..., description="印象ID")


class ExtraInfo(BaseModel):
    """额外信息"""
    
    now: int = Field(..., description="当前时间")
    logid: str = Field(..., description="日志ID")
    fatal_item_ids: List[str] = Field([], description="致命条目ID列表")
    search_request_id: str = Field("", description="搜索请求ID")


class DiscoverSearchResponse(BaseModel):
    """发现搜索响应"""

    type: int = Field(..., description="类型")
    user_list: List[UserSearchResult] = Field([], description="用户列表")
    challenge_list: Optional[List[Any]] = Field(None, description="挑战列表")
    music_list: Optional[List[Any]] = Field(None, description="音乐列表")
    cursor: int = Field(0, description="游标")
    has_more: int = Field(0, description="是否有更多 (0/1)")
    status_code: int = Field(..., description="状态码")
    qc: str = Field("", description="QC")
    myself_user_id: str = Field(..., description="自己的用户ID")
    rid: str = Field(..., description="请求ID")
    log_pb: LogPb = Field(..., description="日志PB")
    extra: ExtraInfo = Field(..., description="额外信息")
    input_keyword: str = Field(..., description="输入关键词")
    global_doodle_config: GlobalDoodleConfig = Field(..., description="全局涂鸦配置")
    path: str = Field(..., description="路径")

    class Config:
        extra = "allow"
