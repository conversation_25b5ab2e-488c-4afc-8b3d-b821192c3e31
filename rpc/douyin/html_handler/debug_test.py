#!/usr/bin/env python3
"""
抖音HTML请求处理模块调试测试脚本

这个脚本可以直接运行，用于测试和调试真实的抖音HTML请求功能。
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from rpc.douyin.html_handler import (
    AntiCrawlerError,
    DouyinHTMLClient,
    DouyinHTMLConfig,
    InvalidURLError,
    JingxuanRequest,
    MobileShareRequest,
    PCVideoRequest,
    RateLimitError,
)


class DouyinHTMLDebugger:
    """抖音HTML请求调试器"""

    def __init__(self):
        """初始化调试器"""
        self.config = DouyinHTMLConfig(
            enable_proxy=False,  # 调试时禁用代理
            enable_cookie_rotation=False,  # 调试时禁用Cookie轮换
            enable_anti_crawler_detection=True,  # 启用反爬虫检测
            request_timeout=30,
            max_retries=2,
        )
        self.client = DouyinHTMLClient(self.config)

        # 测试用的视频ID（这些可能需要根据实际情况调整）
        self.test_aweme_ids = [
            "7123456789012345678",  # 示例ID
            "7234567890123456789",  # 示例ID
            "7345678901234567890",  # 示例ID
        ]

        # 测试用的用户sec_uid（这些可能需要根据实际情况调整）
        self.test_sec_uids = [
            "MS4wLjABAAAA_example_sec_uid_1",  # 示例sec_uid
            "MS4wLjABAAAA_example_sec_uid_2",  # 示例sec_uid
        ]

    def print_separator(self, title: str):
        """打印分隔符"""
        print(f"\n{'='*60}")
        print(f" {title}")
        print(f"{'='*60}")

    def print_response_info(self, response, request_type: str):
        """打印响应信息"""
        print(f"\n[{request_type}] 响应信息:")
        print(f"  成功: {response.success}")
        print(f"  状态码: {response.status_code}")
        print(f"  响应时间: {response.response_time:.2f}s")
        print(f"  重试次数: {response.retry_count}")
        print(f"  请求ID: {response.request_id}")

        if response.proxy_used:
            print(f"  使用代理: {response.proxy_used}")

        if response.cookie_used:
            print(f"  使用Cookie: {response.cookie_used}")

        if response.success:
            content_length = len(response.html_content) if response.html_content else 0
            print(f"  HTML长度: {content_length} 字符")

            if response.html_content:
                # 检查HTML内容的基本特征
                html_lower = response.html_content.lower()
                has_douyin = "douyin" in html_lower
                has_title = "<title>" in html_lower
                has_body = "<body>" in html_lower

                print(f"  包含'douyin': {has_douyin}")
                print(f"  包含<title>: {has_title}")
                print(f"  包含<body>: {has_body}")

                # 显示HTML片段
                snippet = (
                    response.html_content[:200] + "..." if len(response.html_content) > 200 else response.html_content
                )
                print(f"  HTML片段: {snippet}")
        else:
            print(f"  错误信息: {response.error_message}")

    async def test_jingxuan_pages(self):
        """测试精选页面请求"""
        self.print_separator("测试精选页面请求")

        async with self.client:
            for i, aweme_id in enumerate(self.test_aweme_ids[:2]):  # 只测试前2个
                print(f"\n--- 测试精选页面 {i+1}: {aweme_id} ---")

                try:
                    request = JingxuanRequest(aweme_id=aweme_id)
                    response = await self.client.fetch_jingxuan_page(request)
                    self.print_response_info(response, "精选页面")

                except AntiCrawlerError as e:
                    print(f"❌ 遇到反爬虫保护: {e}")
                except RateLimitError as e:
                    print(f"❌ 遇到限流: {e}")
                    if e.retry_after:
                        print(f"   建议等待 {e.retry_after} 秒后重试")
                except Exception as e:
                    print(f"❌ 未知错误: {e}")

                # 在请求之间添加延迟
                if i < len(self.test_aweme_ids) - 1:
                    await asyncio.sleep(2)

    async def test_mobile_share_pages(self):
        """测试移动端分享页面请求"""
        self.print_separator("测试移动端分享页面请求")

        async with self.client:
            for i, aweme_id in enumerate(self.test_aweme_ids[:2]):  # 只测试前2个
                print(f"\n--- 测试移动端分享页面 {i+1}: {aweme_id} ---")

                try:
                    request = MobileShareRequest(aweme_id=aweme_id)
                    response = await self.client.fetch_mobile_share_page(request)
                    self.print_response_info(response, "移动端分享页面")

                except AntiCrawlerError as e:
                    print(f"❌ 遇到反爬虫保护: {e}")
                except RateLimitError as e:
                    print(f"❌ 遇到限流: {e}")
                except Exception as e:
                    print(f"❌ 未知错误: {e}")

                # 在请求之间添加延迟
                if i < len(self.test_aweme_ids) - 1:
                    await asyncio.sleep(2)

    async def test_pc_video_pages(self):
        """测试PC端视频页面请求"""
        self.print_separator("测试PC端视频页面请求")

        async with self.client:
            for i, aweme_id in enumerate(self.test_aweme_ids[:1]):  # 只测试1个
                print(f"\n--- 测试PC端视频页面 {i+1}: {aweme_id} ---")

                try:
                    request = PCVideoRequest(aweme_id=aweme_id)
                    response = await self.client.fetch_pc_video_page(request)
                    self.print_response_info(response, "PC端视频页面")

                except AntiCrawlerError as e:
                    print(f"❌ 遇到反爬虫保护: {e}")
                except RateLimitError as e:
                    print(f"❌ 遇到限流: {e}")
                except Exception as e:
                    print(f"❌ 未知错误: {e}")

    async def test_url_parsing(self):
        """测试URL解析功能"""
        self.print_separator("测试URL解析功能")

        test_urls = [
            f"https://www.douyin.com/jingxuan?modal_id={self.test_aweme_ids[0]}",
            f"https://m.douyin.com/share/video/{self.test_aweme_ids[0]}",
            f"https://www.douyin.com/video/{self.test_aweme_ids[0]}",
            "https://invalid-domain.com/test",  # 无效URL
        ]

        async with self.client:
            for i, url in enumerate(test_urls):
                print(f"\n--- 测试URL {i+1}: {url} ---")

                try:
                    response = await self.client.fetch_by_url(url, use_proxy=False)
                    self.print_response_info(response, "URL解析")

                except InvalidURLError as e:
                    print(f"❌ 无效URL: {e}")
                except AntiCrawlerError as e:
                    print(f"❌ 遇到反爬虫保护: {e}")
                except Exception as e:
                    print(f"❌ 未知错误: {e}")

                # 在请求之间添加延迟
                if i < len(test_urls) - 1:
                    await asyncio.sleep(2)

    async def test_configuration(self):
        """测试配置功能"""
        self.print_separator("测试配置功能")

        print("当前配置:")
        config_dict = self.config.to_dict()
        for key, value in config_dict.items():
            print(f"  {key}: {value}")

        print(f"\n支持的域名: {self.config.supported_domains}")
        print(f"反爬虫关键词数量: {len(self.config.anti_crawler_keywords)}")

        # 测试配置方法
        print("\n配置方法测试:")
        print(f"  是否支持www.douyin.com: {self.config.is_supported_domain('www.douyin.com')}")
        print(f"  是否支持invalid.com: {self.config.is_supported_domain('invalid.com')}")
        print(f"  重试延迟(第0次): {self.config.get_retry_delay(0)}s")
        print(f"  重试延迟(第1次): {self.config.get_retry_delay(1)}s")
        print(f"  重试延迟(第2次): {self.config.get_retry_delay(2)}s")

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始抖音HTML请求处理模块调试测试")
        print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # 测试配置
            await self.test_configuration()

            # 测试各种页面类型
            await self.test_jingxuan_pages()
            await self.test_mobile_share_pages()
            await self.test_pc_video_pages()

            # 测试URL解析
            await self.test_url_parsing()

            self.print_separator("测试完成")
            print("✅ 所有测试已完成")

        except KeyboardInterrupt:
            print("\n⚠️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中发生未知错误: {e}")
            import traceback

            traceback.print_exc()


async def main():
    """主函数"""
    debugger = DouyinHTMLDebugger()
    await debugger.run_all_tests()


if __name__ == "__main__":
    # 运行调试测试
    asyncio.run(main())
