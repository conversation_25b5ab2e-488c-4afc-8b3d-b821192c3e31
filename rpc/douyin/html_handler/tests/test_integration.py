"""
抖音HTML客户端集成测试

这些测试需要真实的网络连接，用于验证与抖音服务器的实际交互
"""

import pytest

from rpc.douyin.html_handler import (
    AntiCrawlerError,
    DouyinHTMLClient,
    DouyinHTMLConfig,
    InvalidURLError,
    JingxuanRequest,
    MobileShareRequest,
    PCVideoRequest,
)


@pytest.mark.integration
class TestDouyinHTMLClientIntegration:
    """抖音HTML客户端集成测试"""

    @pytest.fixture
    def client(self):
        """创建集成测试客户端"""
        config = DouyinHTMLConfig(
            enable_proxy=False,  # 集成测试时禁用代理
            enable_cookie_rotation=False,  # 集成测试时禁用Cookie轮换
            request_timeout=30,
            max_retries=1,  # 减少重试次数以加快测试
        )
        return DouyinHTMLClient(config)

    @pytest.mark.asyncio
    async def test_fetch_jingxuan_page_real(self, client):
        """测试真实的精选页面请求"""
        # 使用一个可能存在的视频ID进行测试
        # 注意：这个测试可能会因为视频不存在或反爬虫而失败
        request = JingxuanRequest(aweme_id="7123456789012345678")

        async with client:
            try:
                response = await client.fetch_jingxuan_page(request)

                # 检查响应基本结构
                assert hasattr(response, "success")
                assert hasattr(response, "status_code")
                assert hasattr(response, "response_time")

                if response.success:
                    assert response.html_content is not None
                    assert len(response.html_content) > 0
                    assert response.status_code == 200
                    print(f"✅ 精选页面请求成功: {response.get_summary()}")
                else:
                    print(f"⚠️ 精选页面请求失败: {response.error_message}")
                    # 即使失败也不应该抛出异常，而是返回失败响应
                    assert response.success is False

            except AntiCrawlerError as e:
                print(f"⚠️ 遇到反爬虫保护: {e}")
                pytest.skip("遇到反爬虫保护，跳过测试")

    @pytest.mark.asyncio
    async def test_fetch_mobile_share_page_real(self, client):
        """测试真实的移动端分享页面请求"""
        request = MobileShareRequest(aweme_id="7123456789012345678")

        async with client:
            try:
                response = await client.fetch_mobile_share_page(request)

                assert hasattr(response, "success")
                assert hasattr(response, "status_code")

                if response.success:
                    assert response.html_content is not None
                    assert response.status_code == 200
                    print(f"✅ 移动端分享页面请求成功: {response.get_summary()}")
                else:
                    print(f"⚠️ 移动端分享页面请求失败: {response.error_message}")

            except AntiCrawlerError as e:
                print(f"⚠️ 遇到反爬虫保护: {e}")
                pytest.skip("遇到反爬虫保护，跳过测试")

    @pytest.mark.asyncio
    async def test_fetch_pc_video_page_real(self, client):
        """测试真实的PC端视频页面请求"""
        request = PCVideoRequest(aweme_id="7123456789012345678")

        async with client:
            try:
                response = await client.fetch_pc_video_page(request)

                assert hasattr(response, "success")
                assert hasattr(response, "status_code")

                if response.success:
                    assert response.html_content is not None
                    assert response.status_code == 200
                    print(f"✅ PC端视频页面请求成功: {response.get_summary()}")
                else:
                    print(f"⚠️ PC端视频页面请求失败: {response.error_message}")

            except AntiCrawlerError as e:
                print(f"⚠️ 遇到反爬虫保护: {e}")
                pytest.skip("遇到反爬虫保护，跳过测试")

    @pytest.mark.asyncio
    async def test_fetch_by_url_real(self, client):
        """测试根据真实URL自动识别类型"""
        urls = [
            "https://www.douyin.com/jingxuan?modal_id=7123456789012345678",
            "https://m.douyin.com/share/video/7123456789012345678",
            "https://www.douyin.com/video/7123456789012345678",
        ]

        async with client:
            for url in urls:
                try:
                    response = await client.fetch_by_url(url, use_proxy=False)

                    assert hasattr(response, "success")
                    assert hasattr(response, "status_code")

                    if response.success:
                        print(f"✅ URL请求成功 {url}: {response.get_summary()}")
                    else:
                        print(f"⚠️ URL请求失败 {url}: {response.error_message}")

                except AntiCrawlerError as e:
                    print(f"⚠️ URL {url} 遇到反爬虫保护: {e}")
                    continue
                except InvalidURLError as e:
                    print(f"❌ 无效URL {url}: {e}")
                    continue

    @pytest.mark.asyncio
    async def test_invalid_url_handling(self, client):
        """测试无效URL的处理"""
        invalid_urls = [
            "https://invalid-domain.com/test",
            "https://www.baidu.com/",
            "not-a-url",
        ]

        async with client:
            for url in invalid_urls:
                with pytest.raises(InvalidURLError):
                    await client.fetch_by_url(url)

    @pytest.mark.asyncio
    async def test_timeout_handling(self, client):
        """测试超时处理"""
        # 设置很短的超时时间来测试超时处理
        client.config.request_timeout = 0.001  # 1毫秒，几乎肯定会超时

        request = JingxuanRequest(aweme_id="7123456789012345678")

        async with client:
            response = await client.fetch_jingxuan_page(request)

            # 超时应该返回失败响应而不是抛出异常
            assert response.success is False
            assert "timeout" in response.error_message.lower() or "超时" in response.error_message

    @pytest.mark.asyncio
    async def test_response_validation(self, client):
        """测试响应验证"""
        request = JingxuanRequest(aweme_id="7123456789012345678")

        async with client:
            try:
                response = await client.fetch_jingxuan_page(request)

                # 验证响应结构
                assert hasattr(response, "success")
                assert hasattr(response, "status_code")
                assert hasattr(response, "headers")
                assert hasattr(response, "url")
                assert hasattr(response, "response_time")
                assert hasattr(response, "retry_count")
                assert hasattr(response, "request_id")
                assert hasattr(response, "timestamp")

                # 验证响应时间为正数
                assert response.response_time >= 0

                # 验证重试次数为非负数
                assert response.retry_count >= 0

                # 验证请求ID不为空
                assert response.request_id is not None
                assert len(response.request_id) > 0

                # 验证时间戳
                assert response.timestamp is not None
                assert response.timestamp > 0

                print(f"✅ 响应验证通过: {response.get_summary()}")

            except AntiCrawlerError as e:
                print(f"⚠️ 遇到反爬虫保护，跳过验证: {e}")
                pytest.skip("遇到反爬虫保护，跳过测试")


@pytest.mark.integration
@pytest.mark.slow
class TestDouyinHTMLClientPerformance:
    """抖音HTML客户端性能测试"""

    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求性能"""
        import asyncio

        config = DouyinHTMLConfig(enable_proxy=False, enable_cookie_rotation=False, max_retries=1)

        async def make_request(client, aweme_id):
            request = JingxuanRequest(aweme_id=aweme_id)
            return await client.fetch_jingxuan_page(request)

        async with DouyinHTMLClient(config) as client:
            # 创建多个并发请求
            tasks = []
            for i in range(3):  # 限制并发数以避免被限流
                aweme_id = f"712345678901234567{i}"
                tasks.append(make_request(client, aweme_id))

            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)

                success_count = 0
                for i, response in enumerate(responses):
                    if isinstance(response, Exception):
                        print(f"请求 {i} 异常: {response}")
                    elif hasattr(response, "success") and response.success:
                        success_count += 1
                        print(f"请求 {i} 成功: {response.get_summary()}")
                    else:
                        print(f"请求 {i} 失败: {getattr(response, 'error_message', 'Unknown error')}")

                print(f"并发测试完成: {success_count}/{len(tasks)} 成功")

            except Exception as e:
                print(f"并发测试异常: {e}")
                pytest.skip("并发测试遇到异常，跳过测试")
