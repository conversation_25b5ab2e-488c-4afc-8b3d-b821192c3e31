"""
抖音新架构使用示例

展示如何使用基于三步增强模式的新抖音 API 客户端
"""

import asyncio

# 导入新架构的组件
from rpc.douyin import (  # 便捷函数（推荐使用）; API 类; 模型; 配置; 客户端管理器
    AsyncDouyinAPI,
    DouyinConfig,
    SearchInfoRequest,
    VideoDetailRequest,
    get_video_comments,
    get_video_detail,
    pong,
    search_videos,
)


def example_basic_usage():
    """基础使用示例 - 使用便捷函数"""
    print("=== 抖音新架构基础使用示例 ===")

    try:
        # 1. 测试连通性
        print("\n1. 测试连通性")
        result = pong()
        print(f"✅ 连通性测试成功: {result.get('slideshow', {}).get('author', 'Unknown')}")

        # 2. 搜索视频（使用便捷函数）
        print("\n2. 搜索视频")
        search_result = search_videos(keyword="美食", count=5)
        print(f"✅ 搜索到 {len(search_result.data)} 个视频")

        # 3. 获取视频详情
        if search_result.data:
            first_video = search_result.data[0]
            print(f"\n3. 获取视频详情: {first_video.aweme_id}")
            detail_result = get_video_detail(first_video.aweme_id)
            print(f"✅ 视频详情获取成功: {detail_result.aweme_detail.desc[:50]}...")

            # 4. 获取视频评论
            print(f"\n4. 获取视频评论: {first_video.aweme_id}")
            comments_result = get_video_comments(first_video.aweme_id, count=3)
            print(f"✅ 获取到 {len(comments_result.comments)} 条评论")

    except Exception as e:
        print(f"❌ 基础使用示例失败: {e}")


def example_api_class_usage():
    """API 类使用示例"""
    print("\n=== API 类使用示例 ===")

    try:
        # 创建 API 实例
        api = AsyncDouyinAPI()

        # 使用请求模型
        search_request = SearchInfoRequest(keyword="科技", count=3, offset=0)

        result = api.search_videos(search_request)
        print(f"✅ API 类搜索成功: {len(result.data)} 个视频")

        for video in result.data[:2]:
            print(f"  - {video.aweme_info.desc[:30]}...")

    except Exception as e:
        print(f"❌ API 类使用示例失败: {e}")


async def example_async_usage():
    """异步使用示例"""
    print("\n=== 异步使用示例 ===")

    try:
        # 创建异步 API 实例
        async_api = AsyncDouyinAPI()

        # 异步搜索
        search_request = SearchInfoRequest(keyword="旅游", count=3, offset=0)

        result = await async_api.search_videos(search_request)
        print(f"✅ 异步搜索成功: {len(result.data)} 个视频")

        # 并发获取视频详情
        if result.data:
            tasks = []
            for video in result.data[:2]:
                detail_request = VideoDetailRequest(aweme_id=video.aweme_id)
                task = async_api.get_video_detail(detail_request)
                tasks.append(task)

            details = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for d in details if not isinstance(d, Exception))
            print(f"✅ 并发获取详情成功: {success_count}/{len(tasks)} 个")

    except Exception as e:
        print(f"❌ 异步使用示例失败: {e}")


async def example_custom_client():
    """自定义客户端示例"""
    print("\n=== 自定义客户端示例 ===")

    try:
        # 创建自定义配置
        config = DouyinConfig(
            timeout=60.0,
            enable_sign=True,
            use_javascript_sign=True,
            sign_cache_enabled=True,
        )

        # 使用预配置的异步客户端实例
        from rpc.douyin import async_douyin_api

        # 测试连通性
        result = await async_douyin_api.pong()
        print("✅ 异步客户端测试成功")

    except Exception as e:
        print(f"❌ 自定义客户端示例失败: {e}")


async def example_async_custom_client():
    """异步自定义客户端示例"""
    print("\n=== 异步自定义客户端示例 ===")

    try:
        # 使用预配置的异步客户端实例
        from rpc.douyin import async_douyin_api

        # 测试连通性
        result = await async_douyin_api.pong()
        print("✅ 异步客户端测试成功")

    except Exception as e:
        print(f"❌ 异步自定义客户端示例失败: {e}")


async def example_configuration():
    """配置示例"""
    print("\n=== 配置示例 ===")

    try:
        # 创建不同的配置
        configs = [
            DouyinConfig(enable_sign=False),  # 禁用签名
            DouyinConfig(use_javascript_sign=False),  # 使用 Python 签名
            DouyinConfig(sign_cache_enabled=False),  # 禁用签名缓存
        ]

        for i, config in enumerate(configs, 1):
            print(f"\n配置 {i}:")
            print(f"  - 启用签名: {config.enable_sign}")
            print(f"  - JavaScript 签名: {config.use_javascript_sign}")
            print(f"  - 签名缓存: {config.sign_cache_enabled}")

            # 使用预配置的异步客户端实例进行测试
            from rpc.douyin import async_douyin_api

            try:
                result = await async_douyin_api.pong()
                print(f"  ✅ 配置 {i} 测试成功")
            except Exception as e:
                print(f"  ❌ 配置 {i} 测试失败: {e}")

    except Exception as e:
        print(f"❌ 配置示例失败: {e}")


async def main():
    """主函数"""
    print("🚀 抖音新架构示例程序启动")

    try:
        # 基础使用示例
        example_basic_usage()

        # API 类使用示例
        example_api_class_usage()

        # 异步使用示例
        await example_async_usage()

        # 自定义客户端示例
        await example_custom_client()

        # 异步自定义客户端示例
        await example_async_custom_client()

        # 配置示例
        await example_configuration()

        print("\n🎉 所有示例执行完成！")

    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
