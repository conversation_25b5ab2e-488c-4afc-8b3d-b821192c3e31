"""
抖音算签功能使用示例

展示如何使用算签功能来增强 API 请求
"""

import asyncio

from rpc.douyin import DouyinConfig, async_douyin_api
from rpc.douyin.utils import DouyinSignClient, DouyinSigner


async def basic_sign_example():
    """基础算签示例"""
    print("🔐 基础算签示例")
    print("-" * 40)

    # 创建算签器
    signer = DouyinSigner(use_javascript=False)  # 使用 Python 算签

    # 准备参数
    uri = "/aweme/v1/web/aweme/search/"
    query_params = "device_platform=webapp&aid=6383&keyword=美食"
    user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"

    try:
        # 生成算签
        a_bogus = await signer.sign(uri, query_params, user_agent)

        print("✅ 算签成功")
        print(f"   URI: {uri}")
        print(f"   查询参数: {query_params}")
        print(f"   算签结果: {a_bogus}")

        # 使用参数字典的方式
        params = {"device_platform": "webapp", "aid": "6383", "keyword": "美食"}

        a_bogus2 = await signer.sign_url_params(uri, params, user_agent)
        print(f"   参数字典算签: {a_bogus2}")

    except Exception as e:
        print(f"❌ 算签失败: {e}")


async def advanced_sign_client_example():
    """高级算签客户端示例"""
    print("\n🚀 高级算签客户端示例")
    print("-" * 40)

    # 创建高级算签客户端
    sign_client = DouyinSignClient(
        use_javascript=False,  # 使用 Python 算签
        enable_cache=True,  # 启用缓存
        cache_ttl=300,  # 缓存5分钟
        max_retries=3,  # 最大重试3次
        retry_delay=1.0,  # 重试延迟1秒
    )

    try:
        # 测试多个不同的请求
        requests = [
            {"uri": "/aweme/v1/web/aweme/search/", "params": {"keyword": "美食", "aid": "6383"}, "desc": "搜索美食"},
            {
                "uri": "/aweme/v1/web/aweme/detail/",
                "params": {"aweme_id": "123456", "aid": "6383"},
                "desc": "获取视频详情",
            },
            {
                "uri": "/aweme/v1/web/comment/list/",
                "params": {"aweme_id": "123456", "cursor": "0"},
                "desc": "获取评论列表",
            },
        ]

        user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"

        for req in requests:
            a_bogus = await sign_client.sign_url_params(req["uri"], req["params"], user_agent)
            print(f"✅ {req['desc']}: {a_bogus[:30]}...")

        # 显示统计信息
        stats = sign_client.get_stats()
        print("\n📊 算签统计:")
        print(f"   总请求数: {stats['total_requests']}")
        print(f"   缓存命中: {stats['cache_hits']}")
        print(f"   缓存未命中: {stats['cache_misses']}")
        print(f"   缓存命中率: {stats['cache_hit_rate']:.2%}")
        print(f"   平均耗时: {stats['average_time']:.3f}秒")
        print(f"   错误率: {stats['error_rate']:.2%}")

    except Exception as e:
        print(f"❌ 高级算签失败: {e}")


async def integrated_client_example():
    """集成客户端示例"""
    print("\n🔗 集成客户端示例")
    print("-" * 40)

    # 创建配置（启用算签）
    config = DouyinConfig(
        enable_sign=True,  # 启用算签
        use_javascript_sign=False,  # 使用 Python 算签
        sign_cache_enabled=True,  # 启用缓存
        sign_cache_ttl=300,  # 缓存5分钟
        enable_logging=True,  # 启用日志
    )

    try:
        client = async_douyin_api.async_client
        print("✅ 客户端初始化成功")
        print(f"   算签器类型: {client._sign_client.signer_type if client._sign_client else 'None'}")

        # 获取算签统计
        stats = client.get_sign_stats()
        if stats:
            print(f"   初始统计: {stats}")

        # 注意：实际的 API 调用需要真实的参数
        # 这里只演示算签功能的集成
        print("\n💡 算签功能已集成到所有 API 方法中")
        print("   当调用 API 方法时，会自动添加 a_bogus 参数")

        # 清空缓存示例
        client.clear_sign_cache()
        print("✅ 算签缓存已清空")

    except Exception as e:
        print(f"❌ 集成客户端示例失败: {e}")


async def javascript_sign_example():
    """JavaScript 算签示例（如果可用）"""
    print("\n🌐 JavaScript 算签示例")
    print("-" * 40)

    try:
        # 尝试创建 JavaScript 算签器
        signer = DouyinSigner(use_javascript=True)

        if signer.is_javascript_available:
            print("✅ JavaScript 算签可用")

            # 测试 JavaScript 算签
            a_bogus = await signer.sign(
                uri="/aweme/v1/web/aweme/search/",
                query_params="device_platform=webapp&aid=6383&keyword=测试",
                user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            )

            print(f"   JavaScript 算签结果: {a_bogus}")
            print(f"   当前算签器类型: {signer.current_signer_type}")

        else:
            print("⚠️ JavaScript 算签不可用")
            print("   请安装 PyExecJS: pip install PyExecJS")
            print("   或者使用 Python 算签作为备用方案")

    except Exception as e:
        print(f"❌ JavaScript 算签示例失败: {e}")


async def performance_test_example():
    """性能测试示例"""
    print("\n⚡ 性能测试示例")
    print("-" * 40)

    import time

    # 创建算签客户端
    sign_client = DouyinSignClient(use_javascript=False, enable_cache=True, cache_ttl=60)

    try:
        # 准备测试参数
        uri = "/aweme/v1/web/aweme/search/"
        user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"

        # 测试不同的查询参数
        test_params = [
            {"keyword": "美食", "aid": "6383"},
            {"keyword": "旅游", "aid": "6383"},
            {"keyword": "音乐", "aid": "6383"},
            {"keyword": "美食", "aid": "6383"},  # 重复，测试缓存
            {"keyword": "舞蹈", "aid": "6383"},
        ]

        print(f"开始性能测试，共 {len(test_params)} 个请求...")

        start_time = time.time()

        for i, params in enumerate(test_params, 1):
            req_start = time.time()
            a_bogus = await sign_client.sign_url_params(uri, params, user_agent)
            req_time = time.time() - req_start

            print(f"   请求 {i}: {req_time:.3f}秒 - {params['keyword']}")

        total_time = time.time() - start_time

        # 显示性能统计
        stats = sign_client.get_stats()
        print("\n📈 性能统计:")
        print(f"   总耗时: {total_time:.3f}秒")
        print(f"   平均耗时: {total_time/len(test_params):.3f}秒/请求")
        print(f"   缓存命中率: {stats['cache_hit_rate']:.2%}")
        print(f"   QPS: {len(test_params)/total_time:.1f} 请求/秒")

    except Exception as e:
        print(f"❌ 性能测试失败: {e}")


async def main():
    """运行所有示例"""
    print("🚀 抖音算签功能使用示例")
    print("=" * 60)

    print("\n⚠️  注意事项:")
    print("1. 这些示例使用简化的 Python 算签实现")
    print("2. 实际使用时建议使用 JavaScript 算签以获得更好的兼容性")
    print("3. 算签功能已自动集成到主客户端中")
    print("4. 可通过配置文件自定义算签行为")

    try:
        # 运行各种示例
        await basic_sign_example()
        await advanced_sign_client_example()
        await integrated_client_example()
        await javascript_sign_example()
        await performance_test_example()

        print("\n" + "=" * 60)
        print("🎉 所有算签示例运行完成！")

    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
