# -*- coding: utf-8 -*-
"""
简单使用示例

演示如何在实际项目中使用验证参数自动生成功能
"""

import asyncio

from rpc.douyin import async_douyin_api


async def example_with_auto_params():
    """使用自动参数生成的示例"""
    print("=== 使用自动参数生成功能 ===")

    # 1. (可选) 配置日志
    # from rpc.douyin import client_manager
    # client_manager.enable_logging(log_level="INFO")

    # 2. 使用预创建的 API 实例
    client = async_douyin_api.async_client
    print("✓ 客户端已准备就绪")

    # 3. 自动生成验证参数
    try:
        verify_params = await client.get_verify_params()
        print("✓ 验证参数生成成功")
        print(f"  - webid: {verify_params.webid}")
        print(f"  - ms_token: {verify_params.ms_token[:20]}...")
        print(f"  - verify_fp: {verify_params.verify_fp[:30]}...")

    except Exception as e:
        print(f"✗ 验证参数生成失败: {e}")
        return

    # 4. 使用自动参数调用 API（需要真实的 Cookie）
    # 注意：这里使用的是测试 Cookie，实际使用时需要从浏览器获取真实的 Cookie
    test_cookies = {
        "sessionid": "your_session_id_here",
        "tt_webid": "your_tt_webid_here",
        "passport_csrf_token": "your_csrf_token_here",
    }

    try:
        print("正在调用 API...")
        # 使用 async_douyin_api 实例调用
        result = await async_douyin_api.get_user_self_info_auto(cookies=test_cookies)

        # 检查响应
        status_code = result.get("status_code", "unknown")
        if status_code == 0:
            print("✓ API 调用成功")
            user_info = result.get("user", {})
            print(f"  - 用户ID: {user_info.get('uid', 'N/A')}")
            print(f"  - 昵称: {user_info.get('nickname', 'N/A')}")
        else:
            print(f"⚠ API 返回错误状态: {status_code}")
            print(f"  - 错误信息: {result.get('status_msg', 'N/A')}")

    except Exception as e:
        print(f"✗ API 调用失败: {e}")
        print("  这是预期的，因为使用的是测试 Cookie")


async def example_manual_params():
    """手动使用验证参数的示例"""
    print("\n=== 手动使用验证参数 ===")

    from rpc.douyin import VerifyParamsGenerator

    # 1. 创建参数生成器
    generator = VerifyParamsGenerator()

    try:
        # 2. 生成验证参数
        params = await generator.get_common_verify_params()
        print("✓ 验证参数生成成功")

        # 3. 手动构建请求参数
        request_params = {
            "webid": params.webid,
            "msToken": params.ms_token,
            "verifyFp": params.verify_fp,
            "fp": params.verify_fp,
        }

        if params.uifid:
            request_params["uifid"] = params.uifid

        print("✓ 请求参数构建完成")
        print(f"  - 参数数量: {len(request_params)}")

        # 4. 这些参数可以用于任何抖音 API 调用
        print("✓ 参数可用于 API 调用")

    except Exception as e:
        print(f"✗ 参数生成失败: {e}")


async def example_fallback_params():
    """降级参数使用示例"""
    print("\n=== 降级参数使用示例 ===")

    from rpc.douyin import VerifyParamsGenerator

    # 1. 创建参数生成器
    generator = VerifyParamsGenerator()

    # 2. 直接使用假参数（不依赖网络）
    fake_params = generator.get_fake_verify_params()
    print("✓ 假参数生成成功")
    print(f"  - webid: {fake_params.webid}")
    print(f"  - ms_token: {fake_params.ms_token[:20]}...")
    print(f"  - verify_fp: {fake_params.verify_fp[:30]}...")

    # 3. 假参数的优势
    print("✓ 假参数优势:")
    print("  - 不依赖网络连接")
    print("  - 生成速度快")
    print("  - 始终可用")
    print("  - 适合测试环境")


async def example_parameter_caching():
    """参数缓存使用示例"""
    print("\n=== 参数缓存使用示例 ===")

    client = async_douyin_api.async_client
    # 1. 第一次获取（会生成新参数）
    print("第一次获取参数...")
    params1 = await client.get_verify_params()
    webid1 = params1.webid

    # 2. 第二次获取（使用缓存）
    print("第二次获取参数（使用缓存）...")
    params2 = await client.get_verify_params()
    webid2 = params2.webid

    if webid1 == webid2:
        print("✓ 缓存工作正常")
    else:
        print("⚠ 缓存可能未工作")

    # 3. 强制刷新
    print("强制刷新参数...")
    params3 = await client.get_verify_params(force_refresh=True)
    webid3 = params3.webid

    print(f"✓ 参数变化: {webid1} -> {webid3}")


def print_usage_tips():
    """打印使用提示"""
    print("\n" + "=" * 50)
    print("📝 使用提示")
    print("=" * 50)
    print("1. 真实 Cookie 获取方法:")
    print("   - 打开浏览器，访问 https://www.douyin.com")
    print("   - 登录你的抖音账号")
    print("   - 按 F12 打开开发者工具")
    print("   - 在 Network 标签页中找到任意请求")
    print("   - 复制 Cookie 字符串")
    print()
    print("2. 参数使用建议:")
    print("   - 优先使用真实参数（成功率更高）")
    print("   - 网络不稳定时使用假参数")
    print("   - 定期刷新参数以保持有效性")
    print()
    print("3. 错误处理:")
    print("   - 始终包含异常处理")
    print("   - 准备降级方案")
    print("   - 记录错误日志便于调试")


async def main():
    """主函数"""
    print("抖音验证参数自动生成 - 简单使用示例")
    print("=" * 50)

    # 运行所有示例
    await example_with_auto_params()
    await example_manual_params()
    await example_fallback_params()
    await example_parameter_caching()

    # 打印使用提示
    print_usage_tips()

    print("\n🎉 示例演示完成!")
    print("现在你可以在自己的项目中使用这些功能了。")


if __name__ == "__main__":
    asyncio.run(main())
