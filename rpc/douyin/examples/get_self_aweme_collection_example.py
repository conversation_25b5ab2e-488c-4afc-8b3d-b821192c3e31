#!/usr/bin/env python3
"""
获取用户收藏夹列表示例

展示如何使用 get_self_aweme_collection 方法获取当前用户的收藏夹列表
"""

import asyncio

from rpc.douyin import (
    Do<PERSON>inClientError,
    Do<PERSON>inResponseError,
    DouyinValidationError,
    SelfAwemeCollectionRequest,
    async_douyin_api,
)


async def get_self_collection_example():
    """获取用户收藏夹列表示例"""

    print("📁 获取用户收藏夹列表示例")
    print("=" * 50)

    client = async_douyin_api.async_client
    try:
        # 创建请求参数
        request = SelfAwemeCollectionRequest(
            cursor=0,
            count=20,  # 每页获取20个收藏夹
        )

        # 设置 Cookie（需要从浏览器获取真实的 Cookie）
        # 注意：这里需要替换为真实的 Cookie 字符串
        cookies = (
            """
        sessionid=your_session_id;
        tt_webid=your_tt_webid;
        passport_csrf_token=your_csrf_token;
        passport_csrf_token_default=your_csrf_token_default;
        d_ticket=your_d_ticket;
        n_mh=your_n_mh;
        sso_uid_tt=your_sso_uid_tt;
        sso_uid_tt_ss=your_sso_uid_tt_ss;
        toutiao_sso_user=your_toutiao_sso_user;
        toutiao_sso_user_ss=your_toutiao_sso_user_ss;
        sid_ucp_sso_v1=your_sid_ucp_sso_v1;
        ssid_ucp_sso_v1=your_ssid_ucp_sso_v1;
        uid_tt=your_uid_tt;
        uid_tt_ss=your_uid_tt_ss;
        sid_tt=your_sid_tt;
        sessionid_ss=your_sessionid_ss;
        store-region=your_store_region;
        store-region-src=your_store_region_src
        """.strip()
            .replace("\n", "")
            .replace(" ", "")
        )

        print("📋 请求参数:")
        print(f"  - cursor: {request.cursor}")
        print(f"  - count: {request.count}")
        print()

        print("🚀 开始获取收藏夹列表...")

        # 调用 API
        response = await client.get_self_aweme_collection(request, cookies)

        print("✅ 收藏夹列表获取成功！")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📁 收藏夹数量: {len(response.collects_list)}")
        print(f"🔄 当前游标: {response.cursor}")
        print(f"📄 是否有更多: {response.has_more_collections}")
        print()

        # 显示收藏夹详情
        if response.collects_list:
            print("📂 收藏夹列表:")
            print("-" * 30)
            for i, collection in enumerate(response.collects_list, 1):
                print(f"  {i}. {collection.name}")
                print(f"     ID: {collection.collects_id}")
                print(f"     视频数量: {collection.count}")
                if collection.cover:
                    print("     有封面: ✅")
                else:
                    print("     有封面: ❌")
                print()
        else:
            print("📭 暂无收藏夹")

    except DouyinResponseError as e:
        print(f"❌ API 响应错误: {e}")
        print("💡 可能的原因:")
        print("   - Cookie 已过期或无效")
        print("   - 账号未登录")
        print("   - 请求频率过高被限制")

    except DouyinValidationError as e:
        print(f"❌ 数据验证错误: {e}")
        print("💡 可能的原因:")
        print("   - API 响应格式发生变化")
        print("   - 响应数据不完整")

    except DouyinClientError as e:
        print(f"❌ 客户端错误: {e}")
        print("💡 可能的原因:")
        print("   - 网络连接问题")
        print("   - 请求超时")
        print("   - 算签服务异常")

    except Exception as e:
        print(f"❌ 未知错误: {type(e).__name__}: {e}")


async def get_all_collections_with_pagination():
    """分页获取所有收藏夹示例"""

    print("\n📚 分页获取所有收藏夹示例")
    print("=" * 50)

    client = async_douyin_api.async_client
    try:
        all_collections = []
        cursor = 0
        page = 1

        # 设置真实的 Cookie
        cookies = "your_real_cookies_here"

        while True:
            print(f"📄 正在获取第 {page} 页...")

            request = SelfAwemeCollectionRequest(
                cursor=cursor,
                count=10,
            )

            response = await client.get_self_aweme_collection(request, cookies)

            if response.status_code != 0:
                print(f"❌ 第 {page} 页获取失败，状态码: {response.status_code}")
                break

            all_collections.extend(response.collects_list)
            print(f"✅ 第 {page} 页获取成功，本页收藏夹数量: {len(response.collects_list)}")

            # 检查是否还有更多数据
            if not response.has_more_collections:
                print("📄 已获取所有收藏夹")
                break

            # 更新游标
            cursor = response.cursor
            page += 1

            # 添加延迟避免请求过快
            await asyncio.sleep(1)

        print(f"\n🎉 总共获取到 {len(all_collections)} 个收藏夹")

        # 显示统计信息
        if all_collections:
            total_videos = sum(collection.count for collection in all_collections)
            print("📊 统计信息:")
            print(f"   - 收藏夹总数: {len(all_collections)}")
            print(f"   - 收藏视频总数: {total_videos}")
            print(f"   - 平均每个收藏夹视频数: {total_videos / len(all_collections):.1f}")

    except Exception as e:
        print(f"❌ 分页获取失败: {type(e).__name__}: {e}")


async def main():
    """主函数"""
    await get_self_collection_example()
    await get_all_collections_with_pagination()


if __name__ == "__main__":
    asyncio.run(main())
