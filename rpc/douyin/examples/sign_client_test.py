#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DouyinSignClient 适配器测试

测试新的 DouyinSignClient 适配器是否正常工作
"""

import asyncio

from rpc.douyin.utils import DouyinSignClient


async def test_sign_client():
    """测试 DouyinSignClient"""
    print("=== 测试 DouyinSignClient 适配器 ===")

    try:
        # 创建签名客户端
        sign_client = DouyinSignClient(use_javascript=True, enable_cache=True, cache_ttl=300)

        # 测试参数
        uri = "/aweme/v1/web/aweme/detail/"
        query_params = "device_platform=webapp&aid=6383&channel=channel_pc_web&aweme_id=7123456789"
        user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        cookies = "sessionid=test_session; tt_webid=test_webid"

        # 第一次调用（应该是缓存未命中）
        print("第一次调用签名...")
        a_bogus_1 = await sign_client.sign(uri, query_params, user_agent, cookies)
        print(f"a_bogus: {a_bogus_1}")

        # 第二次调用（应该是缓存命中）
        print("\n第二次调用签名（测试缓存）...")
        a_bogus_2 = await sign_client.sign(uri, query_params, user_agent, cookies)
        print(f"a_bogus: {a_bogus_2}")

        # 验证缓存是否工作
        if a_bogus_1 == a_bogus_2:
            print("✓ 缓存工作正常")
        else:
            print("✗ 缓存可能未工作")

        # 获取统计信息
        stats = sign_client.get_stats()
        print("\n统计信息:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功请求数: {stats['successful_requests']}")
        print(f"  失败请求数: {stats['failed_requests']}")
        print(f"  缓存命中数: {stats['cache_hits']}")
        print(f"  缓存未命中数: {stats['cache_misses']}")
        print(f"  缓存大小: {sign_client.get_cache_size()}")

        # 测试清空缓存
        print(f"\n清空缓存前缓存大小: {sign_client.get_cache_size()}")
        sign_client.clear_cache()
        print(f"清空缓存后缓存大小: {sign_client.get_cache_size()}")

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        return False


async def test_different_parameters():
    """测试不同参数的签名"""
    print("\n=== 测试不同参数的签名 ===")

    try:
        sign_client = DouyinSignClient(use_javascript=True)

        # 测试不同的 URI
        test_cases = [
            {
                "name": "视频详情",
                "uri": "/aweme/v1/web/aweme/detail/",
                "query_params": "device_platform=webapp&aid=6383&aweme_id=7123456789",
            },
            {
                "name": "评论接口",
                "uri": "/aweme/v1/web/comment/list/reply",
                "query_params": "device_platform=webapp&aid=6383&comment_id=7123456789",
            },
            {
                "name": "用户信息",
                "uri": "/aweme/v1/web/im/user/info/",
                "query_params": "device_platform=webapp&aid=6383&sec_user_id=test_user",
            },
        ]

        user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
        cookies = "sessionid=test_session"

        for case in test_cases:
            print(f"\n测试 {case['name']}:")
            a_bogus = await sign_client.sign(case["uri"], case["query_params"], user_agent, cookies)
            print(f"  URI: {case['uri']}")
            print(f"  a_bogus: {a_bogus[:50]}...")

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("DouyinSignClient 适配器测试开始")
    print("=" * 60)

    results = []

    # 测试基本功能
    results.append(await test_sign_client())

    # 测试不同参数
    results.append(await test_different_parameters())

    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"  基本功能测试: {'✓' if results[0] else '✗'}")
    print(f"  不同参数测试: {'✓' if results[1] else '✗'}")

    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")

    if success_count == total_count:
        print("🎉 所有测试通过！DouyinSignClient 适配器工作正常")
    else:
        print("⚠️  部分测试失败")


if __name__ == "__main__":
    asyncio.run(main())
