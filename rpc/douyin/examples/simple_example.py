"""
抖音 API 客户端简单使用示例

展示如何使用字符串格式的 Cookie
"""

import asyncio

from rpc.douyin import CollectVideoListRequest, async_douyin_api


async def simple_example():
    """简单的使用示例"""

    print("🎬 抖音收藏视频列表获取示例")
    print("=" * 50)

    # 使用预配置的客户端
    client = async_douyin_api.client
    try:
        # 构建请求参数（需要替换为真实值）
        request = CollectVideoListRequest(
            collects_id="7495633625980131112",  # 从浏览器获取
            cursor=0,
            count=10,
            webid="7515479047155254819",
            uifid="7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084fdec4affe6c9400d15eaddd8ff3b64d14c735cab26aa9036f7251b11b3d69039604ab487e442c3433dc7853a3c5a888eaaf76bba74db9720ad0e9745d2e03363cccaf2007ff52a75138033259087bd0ccabcf8b40237b896087580933a1ecb389f2f834b2983cacb8f4f1423c7ee4b63d9a8ce2a94838412966a9e425756b09e6",
            msToken="tawjmnjZf9BfOvlMg0A6-qgcJlkpSIrHtEWyr2lc-KNrlXe4HhFs_XIAj94DbUmPTKPqgsMSTimJaOwj48DfMsWia9lUKovfTETCgeyf7Zxy-N-3wdoIyBDNxvgOUcjgyUGScBYMmqaKkKmZAa5IEs8lHuO9r4hs8gLGluJ8zMLjLKOPciw%3D",
            a_bogus="mysfhzSwYNmccdFSmKpDt3cUMS2%2FNTSy-lixWoaTHOYbGXUPoYPykPaCcxwodUxJu8p0hoAHfDalYnVbKzXTZKCpumkDSOXRQU599W8LgqqXPUssDqmxC0XzLwBKlQiLe%2FCjEA8RAssrIVdAVrV%2Fld2aS5ToQcDdWNMjDMLy7EWgfWSkhn3wO9Ddx6aC0-nU",
            verifyFp="verify_mckr40mp_XNxX8Elu_EIaw_4as5_8mTS_F47HgvTuh94m",
            fp="verify_mckr40mp_XNxX8Elu_EIaw_4as5_8mTS_F47HgvTuh94m",
        )

        # 使用字符串格式的 Cookie（推荐方式）
        # 直接从浏览器开发者工具中复制完整的 Cookie 字符串
        cookies = "UIFID_TEMP=7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084f44268a63da6486779858d819c2bf0a284b20f7c0a93bab48780171b672930a89df9ce37b46f62f404af466a5ab86f43f; fpk1=U2FsdGVkX1+QTIeEYthMRRM9wQIGvq+McrtWCsHqvETK4N57O8QilzFeZW6BfnPMDZbCGnjGkwgIRODv+0mvLQ==; fpk2=8381c048a9d70230af13a12a76663dc4; UIFID=7457f5ef2178e63069f24974fa04cbf9321b3fa7afdb44208071bc08e7f7084fdec4affe6c9400d15eaddd8ff3b64d14c735cab26aa9036f7251b11b3d69039604ab487e442c3433dc7853a3c5a888eaaf76bba74db9720ad0e9745d2e03363cccaf2007ff52a75138033259087bd0ccabcf8b40237b896087580933a1ecb389f2f834b2983cacb8f4f1423c7ee4b63d9a8ce2a94838412966a9e425756b09e6; bd_ticket_guard_client_web_domain=2; hevc_supported=true; is_staff_user=false; SelfTabRedDotControl=%5B%5D; __security_mc_1_s_sdk_cert_key=6342a58d-498e-8e7a; SearchMultiColumnLandingAbVer=2; SEARCH_RESULT_LIST_TYPE=%22multi%22; passport_csrf_token=28036da03200e6a091b8da4339613534; passport_csrf_token_default=28036da03200e6a091b8da4339613534; xgplayer_user_id=75253824406; d_ticket=2c5dd49063a1aabbe93df14cdae3e2f433f95; passport_mfa_token=CjURYNKpyd1q7uPQpQDQR%2BMe3kA9p2BOAMK%2Fdvg%2FbDgw5TtolpFLTXqQfXQhW9sObOwqaiAuMhpKCjzfJfXDnwxsFHCSuGR6NNpqSxNUgFTkoJb%2BQ7Nf8Xe8m7UG%2BX562HPkGRWxxDC4N3kHVNpAKrRXQ5LAeooQm8TyDRj2sdFsIAIiAQNzYUzC; __security_server_data_status=1; my_rd=2; login_time=1749050184259; passport_assist_user=CkFshGd02WMHnDIk6TOe7LfJpay9_OYNdJ5HBNpgIWxZ0YaRBvOvJjxgAY3ilVzGRvfWscMgxmFXVvRDWVkcI2mJmhpKCjwAAAAAAAAAAAAATxNsD4LlBv2bX2b6Cj16zpS_VMEONe5RExK-rzkStpdRwyoj3vYCxChNEub_PRR8tNIQm5rzDRiJr9ZUIAEiAQNvxs9D; n_mh=XN-wT6okQcwIAv5AmXX_N01iHgwgFD2o_XPxJxRQouM; sso_uid_tt=f382b873f43d6695959fe02ec7c4a5a4; sso_uid_tt_ss=f382b873f43d6695959fe02ec7c4a5a4; toutiao_sso_user=391dae2309a77075fb5cd94410027f2c; toutiao_sso_user_ss=391dae2309a77075fb5cd94410027f2c; sid_ucp_sso_v1=1.0.0-KGE1NjE1OGRkMmFhY2RhMWRjM2JiNWRjMWZjM2I5MjFhNzlhZjVmYmIKIQjYtOCTq_SNAxDSxoHCBhjvMSAMMJ3RrPEFOAZA9AdIBhoCbGYiIDM5MWRhZTIzMDlhNzcwNzVmYjVjZDk0NDEwMDI3ZjJj; ssid_ucp_sso_v1=1.0.0-KGE1NjE1OGRkMmFhY2RhMWRjM2JiNWRjMWZjM2I5MjFhNzlhZjVmYmIKIQjYtOCTq_SNAxDSxoHCBhjvMSAMMJ3RrPEFOAZA9AdIBhoCbGYiIDM5MWRhZTIzMDlhNzcwNzVmYjVjZDk0NDEwMDI3ZjJj; __security_mc_1_s_sdk_sign_data_key_sso=10cf7269-4d19-8ae3; passport_auth_status=9edd9e97cc6c45766f667bcc2c580f2c%2Cf1c63efdc134231c4ecae7ddd15cbe88; passport_auth_status_ss=9edd9e97cc6c45766f667bcc2c580f2c%2Cf1c63efdc134231c4ecae7ddd15cbe88; uid_tt=c35b2b685df3362a53924cb0d542704a; uid_tt_ss=c35b2b685df3362a53924cb0d542704a; sid_tt=f2505534f35736f56b66dec39d85b952; sessionid=f2505534f35736f56b66dec39d85b952; sessionid_ss=f2505534f35736f56b66dec39d85b952; _bd_ticket_crypt_doamin=2; _bd_ticket_crypt_cookie=c7ec5cb5418bb33e9282a642d193b2a6; __security_mc_1_s_sdk_sign_data_key_web_protect=966389c7-461e-9dc5; session_tlb_tag=sttt%7C11%7C8lBVNPNXNvVrZt7DnYW5Uv_________B1yLiFbtKmhtUSEVq87xv1SYq3bEh7ZgV2TukObQ9yig%3D; xgplayer_device_id=5949599020; enter_pc_once=1; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.6%7D; ttwid=1%7Cei7TJc7mSazdhpdQsJnZVrsha9a4ITKy0QZNNpwOUSA%7C1750334304%7Cee50a9ce65185f6873a31352b024d8378e4293c67f7fcea97719431359a22e5d; douyin.com; xg_device_score=7.714952710620508; device_web_cpu_core=12; device_web_memory_size=8; dy_swidth=1512; dy_sheight=982; s_v_web_id=verify_mckr40mp_XNxX8Elu_EIaw_4as5_8mTS_F47HgvTuh94m; __security_mc_1_s_sdk_crypt_sdk=9b81d32a-4e3d-817a; is_dash_user=1; publish_badge_show_info=%220%2C0%2C0%2C1751387860336%22; sid_guard=f2505534f35736f56b66dec39d85b952%7C1751387864%7C5184000%7CSat%2C+30-Aug-2025+16%3A37%3A44+GMT; sid_ucp_v1=1.0.0-KDZkZmQ4ZTY1MjA2OGVlOTg0NGI4MmU0OTQ3YjEzMDVkYzY1M2M2NzgKGwjYtOCTq_SNAxDYnZDDBhjvMSAMOAZA9AdIBBoCaGwiIGYyNTA1NTM0ZjM1NzM2ZjU2YjY2ZGVjMzlkODViOTUy; ssid_ucp_v1=1.0.0-KDZkZmQ4ZTY1MjA2OGVlOTg0NGI4MmU0OTQ3YjEzMDVkYzY1M2M2NzgKGwjYtOCTq_SNAxDYnZDDBhjvMSAMOAZA9AdIBBoCaGwiIGYyNTA1NTM0ZjM1NzM2ZjU2YjY2ZGVjMzlkODViOTUy; stream_player_status_params=%22%7B%5C%22is_auto_play%5C%22%3A0%2C%5C%22is_full_screen%5C%22%3A0%2C%5C%22is_full_webscreen%5C%22%3A0%2C%5C%22is_mute%5C%22%3A0%2C%5C%22is_speed%5C%22%3A1%2C%5C%22is_visible%5C%22%3A0%7D%22; __ac_signature=_02B4Z6wo00f01.70DBwAAIDC1.CSYeiRu.P-1AiAAJfiC3bW8KgKG6WIuDwiK-xLR.kIBJ1s6-oGOSCeyuxwsHSh4htPZA2WMdmiMaTUSrSjy1QigoIh05SFzTjNM3NJsVbK4DQbM4pebvGP03; download_guide=%223%2F20250702%2F0%22; __ac_nonce=0686557670078915fbf2c; IsDouyinActive=true; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1512%2C%5C%22screen_height%5C%22%3A982%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A12%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A50%7D%22; home_can_add_dy_2_desktop=%221%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRUFXZzYzRGo1b2pqQjV0Y08rdWlEeUdERTVZdTBQKzBBK3BncDVVR2sxQ24yUFNjd3pibHVtcXVuY2lFczlBeUp5NDNtQVBaWEl2NHdUQzBRWTZtdkE9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1751472000000%2F0%2F0%2F1751472590712%22; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1751472000000%2F0%2F0%2F1751473190712%22; passport_fe_beating_status=true; odin_tt=391b5f747276ceb93ba20dc086c5963085d102afcff15df887c6d74ced4a7ef822ee937fdfcd2c154ff86065d4054c7e"

        print("📋 请求参数:")
        print(f"  收藏夹ID: {request.collects_id}")
        print(f"  每页数量: {request.count}")
        print(f"  Cookie 长度: {len(cookies)} 字符")
        print()

        print("🚀 发起请求...")
        response = await client.get_collect_video_list(request, cookies)

        print("✅ 请求成功！")
        print("📊 响应信息:")
        print(f"  状态码: {response.status_code}")
        print(f"  视频数量: {len(response.aweme_list)}")
        print(f"  是否有更多: {response.has_more}")
        print(f"  最大游标: {response.max_cursor}")
        print()

        print("🎥 视频列表:")
        for i, video in enumerate(response.aweme_list, 1):
            print(f"  {i}. {video.desc}")
            print(f"     作者: {video.author.nickname}")
            print(f"     点赞: {video.statistics.digg_count:,}")
            print(f"     评论: {video.statistics.comment_count:,}")
            print(f"     播放: {video.statistics.play_count:,}")
            print(f"     链接: {video.share_url}")
            print()

    except Exception as e:
        print(f"❌ 请求失败: {e}")
        print()
        print("💡 解决方案:")
        print("1. 确保从真实浏览器会话中获取所有参数")
        print("2. 检查 Cookie 是否有效且未过期")
        print("3. 参考 USAGE.md 获取详细的参数获取步骤")
        print("4. 使用 debug_example.py 进行问题诊断")


async def cookie_format_demo():
    """演示不同的 Cookie 格式"""

    print("\n🍪 Cookie 格式演示")
    print("=" * 50)

    client = async_douyin_api.client

    # 演示字符串格式 Cookie 解析
    cookie_string = "sessionid=abc123; tt_webid=def456; passport_csrf_token=ghi789; uid_tt=jkl012"

    print("📝 原始 Cookie 字符串:")
    print(f"  {cookie_string}")
    print()

    parsed_cookies = client._parse_cookie_string(cookie_string)

    print("🔍 解析后的 Cookie 字典:")
    for key, value in parsed_cookies.items():
        print(f"  {key}: {value}")
    print()

    print("✨ 两种使用方式:")
    print("方式一（推荐）- 字符串格式:")
    print(f'  cookies = "{cookie_string}"')
    print()
    print("方式二 - 字典格式:")
    print("  cookies = {")
    for key, value in parsed_cookies.items():
        print(f'    "{key}": "{value}",')
    print("  }")
    print()

    print("💡 提示: 字符串格式更方便，可以直接从浏览器复制粘贴！")


if __name__ == "__main__":
    print("🎯 抖音 API 客户端使用示例")
    print("=" * 60)

    # 运行 Cookie 格式演示
    asyncio.run(cookie_format_demo())

    # 运行简单示例
    asyncio.run(simple_example())

    print("\n📚 更多信息:")
    print("- 详细使用指南: app/rpc/douyin/docs/USAGE.md")
    print("- 完整示例: app/rpc/douyin/examples/example.py")
    print("- 问题诊断: app/rpc/douyin/examples/debug_example.py")
    print("- API 文档: app/rpc/douyin/docs/README.md")
