#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音签名功能使用示例

演示如何使用迁移后的抖音签名功能
"""

import asyncio

from rpc.douyin import DouyinSignFactory, DouyinSignLogic, DouyinSignRequest


async def test_javascript_sign():
    """测试 JavaScript 签名功能"""
    print("=== 测试 JavaScript 签名功能 ===")

    try:
        # 创建签名逻辑实例（默认使用 JavaScript 签名）
        sign_logic = DouyinSignLogic(sign_type=DouyinSignFactory.JAVASCRIPT_SIGN)

        # 创建签名请求
        request = DouyinSignRequest(
            uri="/aweme/v1/web/aweme/detail/",
            query_params="device_platform=webapp&aid=6383&channel=channel_pc_web&aweme_id=7123456789&update_version_code=170400",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            cookies="sessionid=test_session; tt_webid=test_webid",
        )

        # 生成签名
        response = await sign_logic.sign(request)

        print("签名生成成功:")
        print(f"  URI: {request.uri}")
        print(f"  a_bogus: {response.a_bogus}")

        return True

    except FileNotFoundError as e:
        print(f"JavaScript 文件未找到: {e}")
        print("请确保 douyin.js 文件存在于 rpc/douyin/signer/js/ 目录下")
        return False

    except Exception as e:
        print(f"签名生成失败: {e}")
        return False


async def test_playwright_sign():
    """测试 Playwright 签名功能（已移除）"""
    print("\n=== 测试 Playwright 签名功能 ===")

    try:
        # 创建签名逻辑实例（使用 Playwright 签名，应该自动切换到 JavaScript）
        sign_logic = DouyinSignLogic(sign_type="playwright")

        # 创建签名请求
        request = DouyinSignRequest(
            uri="/aweme/v1/web/aweme/detail/",
            query_params="device_platform=webapp&aid=6383&channel=channel_pc_web&aweme_id=7123456789&update_version_code=170400",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            cookies="sessionid=test_session; tt_webid=test_webid",
        )

        # 生成签名
        response = await sign_logic.sign(request)

        print("签名生成成功:")
        print(f"  URI: {request.uri}")
        print(f"  a_bogus: {response.a_bogus}")

        return True

    except NotImplementedError as e:
        print(f"Playwright 签名功能暂未完全实现: {e}")
        print("需要集成 Playwright 环境和相关依赖")
        return False

    except Exception as e:
        print(f"签名生成失败: {e}")
        return False


async def test_factory_pattern():
    """测试工厂模式"""
    print("\n=== 测试工厂模式 ===")

    try:
        # 测试 JavaScript 签名器创建
        js_signer = DouyinSignFactory.get_sign(DouyinSignFactory.JAVASCRIPT_SIGN)
        print(f"JavaScript 签名器创建成功: {type(js_signer).__name__}")

        # 测试 Playwright 签名器创建（应该返回占位符）
        playwright_signer = DouyinSignFactory.get_sign("playwright")
        print(f"Playwright 签名器创建成功: {type(playwright_signer).__name__}")

        # 测试不支持的签名类型
        try:
            unknown_signer = DouyinSignFactory.get_sign("unknown_type")
        except NotImplementedError as e:
            print(f"不支持的签名类型处理正确: {e}")

        return True

    except Exception as e:
        print(f"工厂模式测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("抖音签名功能测试开始")
    print("=" * 50)

    results = []

    # 测试 JavaScript 签名
    results.append(await test_javascript_sign())

    # 测试 Playwright 签名
    results.append(await test_playwright_sign())

    # 测试工厂模式
    results.append(await test_factory_pattern())

    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"  JavaScript 签名: {'✓' if results[0] else '✗'}")
    print(f"  Playwright 签名: {'✓' if results[1] else '✗'}")
    print(f"  工厂模式: {'✓' if results[2] else '✗'}")

    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")

    if success_count == total_count:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查相关配置和依赖")


if __name__ == "__main__":
    asyncio.run(main())
