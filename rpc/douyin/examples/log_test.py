#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志功能测试

测试迁移后的签名模块是否正确使用 loguru 日志系统
"""

import asyncio

from rpc.douyin import DouyinSignFactory, DouyinSignLogic, DouyinSignRequest
from rpc.douyin.utils import DouyinSignClient


async def test_logging():
    """测试日志功能"""
    print("=== 测试 loguru 日志功能 ===")

    try:
        print("\n1. 测试 DouyinSignLogic 日志:")
        # 创建签名逻辑实例
        sign_logic = DouyinSignLogic(sign_type=DouyinSignFactory.JAVASCRIPT_SIGN)

        # 创建一个会导致错误的请求来测试错误日志
        invalid_request = DouyinSignRequest(
            uri="/invalid/uri/", query_params="invalid_params", user_agent="Test-Agent", cookies="test=cookie"
        )

        # 正常请求
        valid_request = DouyinSignRequest(
            uri="/aweme/v1/web/aweme/detail/",
            query_params="device_platform=webapp&aid=6383",
            user_agent="Mozilla/5.0 (Test)",
            cookies="test=cookie",
        )

        print("  - 生成正常签名（应该有 INFO 日志）:")
        response = await sign_logic.sign(valid_request)
        print(f"    签名生成成功: {response.a_bogus[:50]}...")

        print("\n2. 测试 DouyinSignClient 日志:")
        # 创建签名客户端
        sign_client = DouyinSignClient(use_javascript=True, enable_cache=True)

        print("  - 初始化客户端（应该有 INFO 日志）")
        print("  - 生成签名（应该有缓存相关日志）:")

        a_bogus = await sign_client.sign(
            uri="/aweme/v1/web/aweme/detail/",
            query_params="device_platform=webapp&aid=6383",
            user_agent="Mozilla/5.0 (Test)",
            cookies="test=cookie",
        )
        print(f"    签名: {a_bogus[:50]}...")

        print("  - 清空缓存（应该有 INFO 日志）:")
        sign_client.clear_cache()

        print("\n3. 测试错误日志:")
        try:
            # 尝试使用不存在的 JS 文件
            invalid_sign_logic = DouyinSignLogic(
                sign_type=DouyinSignFactory.JAVASCRIPT_SIGN, js_file_path="/nonexistent/path/douyin.js"
            )
        except Exception as e:
            print(f"    预期的错误: {str(e)[:100]}...")

        print("\n4. 测试 Playwright 警告日志:")
        try:
            playwright_logic = DouyinSignLogic(sign_type=DouyinSignFactory.PLAYWRIGHT_SIGN)
            await playwright_logic.sign(valid_request)
        except Exception as e:
            print(f"    预期的 Playwright 错误: {str(e)[:100]}...")

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("抖音签名模块日志功能测试")
    print("=" * 50)
    print("注意观察控制台输出的 loguru 格式日志:")
    print("格式: YYYY-MM-DD HH:MM:SS.mmm | LEVEL | module:function:line - message")
    print("=" * 50)

    success = await test_logging()

    print("\n" + "=" * 50)
    if success:
        print("✅ 日志功能测试完成")
        print("📝 如果看到上述格式的日志输出，说明 loguru 集成成功")
    else:
        print("❌ 日志功能测试失败")

    print("\n💡 日志功能说明:")
    print("  - 使用项目统一的 loguru 日志系统")
    print("  - 自动记录模块名、函数名、行号")
    print("  - 支持不同日志级别（INFO、WARNING、ERROR）")
    print("  - 格式化输出，便于调试和监控")


if __name__ == "__main__":
    asyncio.run(main())
