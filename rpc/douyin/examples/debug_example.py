"""
抖音 API 客户端调试示例

用于调试和测试抖音 API 客户端的问题
"""

import asyncio

from rpc.douyin import CollectVideoListRequest, async_douyin_api


async def debug_request():
    """调试请求示例"""

    # 使用预配置的客户端
    client = async_douyin_api.client
    try:
        # 使用示例中的参数（这些参数可能已过期，仅用于测试）
        request = CollectVideoListRequest(
            collects_id="7495633625980131112",
            cursor=0,
            count=1,  # 只请求1个视频，减少数据量
            webid="7515479047155254819",
            uifid="test_uifid",  # 简化参数
            msToken="test_token",
            a_bogus="test_bogus",
            verifyFp="test_fp",
            fp="test_fp",
        )

        # 不使用 Cookie 先测试
        print("=== 测试不带 Cookie 的请求 ===")
        response = await client.get_collect_video_list(request)
        print(f"成功！状态码: {response.status_code}")

    except Exception as e:
        print(f"请求失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")


async def debug_simple_request():
    """更简单的调试请求"""

    # 使用预配置的客户端
    client = async_douyin_api.client
    try:
        # 直接发起 HTTP 请求，不经过业务逻辑
        print("=== 测试原始 HTTP 请求 ===")

        # 构建最简单的参数
        params = {
            "device_platform": "webapp",
            "aid": "6383",
            "channel": "channel_pc_web",
            "collects_id": "7495633625980131112",
            "cursor": "0",
            "count": "1",
        }

        response = await client._make_request(
            method="GET",
            url="/aweme/v1/web/collects/video/list/",
            params=params,
        )

        print(f"HTTP 状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容类型: {response.headers.get('content-type', 'unknown')}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应前200字符: {response.text[:200]}")

        if response.text.strip().startswith("<"):
            print("⚠️  收到 HTML 响应，可能被重定向到登录页面或错误页面")
        elif not response.text.strip():
            print("⚠️  收到空响应")
        else:
            try:
                json_data = response.json()
                print(f"JSON 解析成功，数据类型: {type(json_data)}")
                if isinstance(json_data, dict):
                    print(f"JSON 键: {list(json_data.keys())}")
            except Exception as e:
                print(f"JSON 解析失败: {e}")

    except Exception as e:
        print(f"原始请求失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")


async def test_with_real_browser_headers():
    """使用真实浏览器请求头测试"""

    # 更完整的请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "referer": "https://www.douyin.com/user/self?showTab=favorite_collection",
        "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not?A_Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
    }

    client = async_douyin_api.client
    try:
        print("=== 测试带完整请求头的请求 ===")

        params = {
            "device_platform": "webapp",
            "aid": "6383",
            "channel": "channel_pc_web",
            "collects_id": "7495633625980131112",
            "cursor": "0",
            "count": "1",
            "version_code": "170400",
            "version_name": "17.4.0",
        }

        response = await client._make_request(
            method="GET",
            url="/aweme/v1/web/collects/video/list/",
            params=params,
            headers=headers,
        )

        print(f"状态码: {response.status_code}")
        print(f"响应类型: {response.headers.get('content-type', 'unknown')}")

        if response.status_code == 200:
            print("✅ 请求成功！")
            try:
                json_data = response.json()
                print(f"JSON 数据类型: {type(json_data)}")
                if isinstance(json_data, dict):
                    print(f"响应键: {list(json_data.keys())}")
                    if "status_code" in json_data:
                        print(f"API 状态码: {json_data['status_code']}")
                    if "status_msg" in json_data:
                        print(f"API 状态消息: {json_data['status_msg']}")
            except Exception as e:
                print(f"JSON 解析失败: {e}")
                print(f"响应内容: {response.text[:500]}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")

    except Exception as e:
        print(f"请求异常: {str(e)}")


if __name__ == "__main__":
    print("🔍 开始调试抖音 API 客户端...")

    print("\n1. 测试简单请求")
    asyncio.run(debug_simple_request())

    print("\n2. 测试带完整请求头的请求")
    asyncio.run(test_with_real_browser_headers())

    print("\n3. 测试业务逻辑请求")
    asyncio.run(debug_request())

    print("\n🔍 调试完成！")
