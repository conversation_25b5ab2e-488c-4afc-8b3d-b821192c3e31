"""
抖音 API 接口类

基于新架构的抖音 API 客户端，提供同步和异步接口
"""

from typing import Any, Dict

import httpx
from loguru import logger
from pydantic import ValidationError

from .client import client_manager
from .config import DouyinConfig
from .exceptions import DouyinResponseError, DouyinValidationError
from .schemas import (
    AwemeCommentsRequest,
    AwemeCommentsResponse,
    DiscoverSearchRequest,
    DiscoverSearchResponse,
    QueryUserRequest,
    QueryUserResponse,
    SearchInfoRequest,
    SearchInfoResponse,
    UserAwemePostsRequest,
    UserAwemePostsResponse,
    UserInfoRequest,
    UserInfoResponse,
    VideoDetailRequest,
    VideoDetailResponse,
)


class AsyncDouyinAPI:
    """
    抖音异步 API 客户端

    提供抖音相关 API 的异步调用接口
    """

    def __init__(self, async_client: httpx.AsyncClient = None, config: DouyinConfig = None):
        """
        初始化抖音异步 API 客户端

        Args:
            async_client: httpx.AsyncClient 实例，如果为 None 则使用默认客户端
            config: 抖音配置
        """
        self.async_client = async_client or client_manager.create_async_client(config=config)
        self.config = config or DouyinConfig()

    def _handle_response(self, response: httpx.Response) -> Dict[str, Any]:
        """
        处理 HTTP 响应

        Args:
            response: HTTP 响应对象

        Returns:
            解析后的 JSON 数据

        Raises:
            DouyinResponseError: 响应错误
        """
        try:
            response.raise_for_status()

            # 检查响应内容类型
            content_type = response.headers.get("content-type", "").lower()
            if "application/json" not in content_type and "text/json" not in content_type:
                logger.warning(f"响应内容类型不是JSON: {content_type}")
                logger.debug(f"响应内容: {response.text[:500]}...")  # 只记录前500个字符

            # 检查响应内容是否为空
            if not response.text.strip():
                raise DouyinResponseError("响应内容为空")

            data = response.json()

            # 检查抖音 API 的状态码
            if isinstance(data, dict) and "status_code" in data:
                if data["status_code"] != 0:
                    error_msg = data.get("status_msg", "未知错误")
                    logger.error(f"抖音 API 返回错误: status_code={data['status_code']}, msg={error_msg}")
                    raise DouyinResponseError(f"抖音 API 错误: {error_msg}")

            return data

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP 状态错误: {e.response.status_code}, URL: {e.request.url}")
            logger.debug(f"响应内容: {e.response.text[:500]}...")
            raise DouyinResponseError(f"HTTP 错误: {e.response.status_code}")
        except ValueError as e:
            logger.error(f"JSON 解析失败: {e}")
            logger.debug(f"响应内容: {response.text[:500]}...")
            logger.debug(f"响应头: {dict(response.headers)}")
            raise DouyinResponseError(f"JSON 解析错误: {e}")

    async def search_videos(self, request: SearchInfoRequest) -> SearchInfoResponse:
        """
        搜索视频

        Mock: rpc/douyin/tests/mock/search_info_by_keyword.json

        Args:
            request: 搜索请求参数

        Returns:
            搜索响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/general/search/single/"

        params = request.model_dump(by_alias=True, exclude_none=True)

        try:
            response = await self.async_client.get(url, params=params)
            data = self._handle_response(response)
            return SearchInfoResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def discover_search(self, request: DiscoverSearchRequest, cookies: str = None) -> DiscoverSearchResponse:
        """
        发现搜索

        Mock: rpc/douyin/tests/mock/discover_search.json

        Args:
            request: 发现搜索请求参数
            cookies: 可选的 cookies 字符串

        Returns:
            发现搜索响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/discover/search/"

        params = request.model_dump(by_alias=True, exclude_none=True)

        # 如果提供了 cookies，则创建临时的请求头覆盖默认设置
        headers = {}
        if cookies:
            headers["Cookie"] = cookies

        try:
            response = await self.async_client.get(url, params=params, headers=headers)
            data = self._handle_response(response)
            return DiscoverSearchResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_video_detail(self, request: VideoDetailRequest, cookies: str = None) -> VideoDetailResponse:
        """
        获取视频详情

        Mock: rpc/douyin/tests/mock/get_video_by_id.json

        Args:
            request: 视频详情请求参数
            cookies: 可选的 cookies 字符串，如果提供则覆盖 account provider 的 cookies

        Returns:
            视频详情响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/aweme/detail/"

        params = {
            "aweme_id": request.aweme_id,
        }

        # 如果提供了 cookies，则创建临时的请求头覆盖默认设置
        headers = {}
        if cookies:
            headers["Cookie"] = cookies

        try:
            response = await self.async_client.get(url, params=params, headers=headers)
            data = self._handle_response(response)
            return VideoDetailResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_video_comments(self, request: AwemeCommentsRequest) -> AwemeCommentsResponse:
        """
        获取视频评论

        Mock: rpc/douyin/tests/mock/get_aweme_comments.json

        Args:
            request: 评论请求参数

        Returns:
            评论响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/comment/list/"

        params = {
            "aweme_id": request.aweme_id,
            "cursor": request.cursor,
            "count": request.count,
            "item_type": request.item_type,
        }

        try:
            response = await self.async_client.get(url, params=params)
            data = self._handle_response(response)
            return AwemeCommentsResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_user_info(self, request: UserInfoRequest) -> UserInfoResponse:
        """
        获取用户信息

        Mock: rpc/douyin/tests/mock/get_user_info.json

        Args:
            request: 用户信息请求参数

        Returns:
            用户信息响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/im/user/info/"

        params = {
            "sec_user_id": request.sec_user_id,
        }

        try:
            response = await self.async_client.get(url, params=params)
            data = self._handle_response(response)
            return UserInfoResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_user_videos(self, request: UserAwemePostsRequest) -> UserAwemePostsResponse:
        """
        获取用户视频列表

        Mock: rpc/douyin/tests/mock/get_user_aweme_posts.json

        Args:
            request: 用户视频请求参数

        Returns:
            用户视频响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/aweme/post/"

        params = {
            "sec_user_id": request.sec_user_id,
            "max_cursor": request.max_cursor,
            "count": request.count,
        }

        try:
            response = await self.async_client.get(url, params=params)
            data = self._handle_response(response)
            return UserAwemePostsResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def query_user(self, request: QueryUserRequest, cookies: str = None) -> QueryUserResponse:
        """
        查询用户信息

        Mock: rpc/douyin/tests/mock/query_user.json

        Args:
            request: 查询用户请求参数
            cookies: 可选的 cookies 字符串

        Returns:
            用户查询响应结果
        """
        url = f"{self.config.base_url}/aweme/v1/web/query/user/"

        # 如果提供了 cookies，则创建临时的请求头覆盖默认设置
        headers = {}
        if cookies:
            headers["Cookie"] = cookies

        try:
            response = await self.async_client.get(url, headers=headers)
            data = self._handle_response(response)
            return QueryUserResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")








    async def pong(self, cookies: str = None) -> bool:
        """
        测试接口连通性和 cookies 有效性

        Mock: 不需要 mock 数据，使用实际的连通性测试

        Args:
            cookies: 可选的 cookies 字符串，如果提供则验证该 cookies 的有效性

        Returns:
            bool: 连通性测试结果，True 表示成功
        """
        try:
            # 如果提供了 cookies，使用抖音的实际接口进行验证
            if cookies:
                # 使用一个简单的抖音接口来验证 cookies
                url = f"{self.config.base_url}/aweme/v1/web/collects/list/"
                headers = {"Cookie": cookies}
                params = {"cursor": 0, "count": 1}

                response = await self.async_client.get(url, params=params, headers=headers)
                data = self._handle_response(response)

                # 检查响应是否表明 cookies 有效
                return isinstance(data, dict) and data.get("status_code") == 0
            else:
                # 不提供 cookies 时，使用通用的连通性测试
                response = await self.async_client.get("https://httpbin.org/json")
                self._handle_response(response)
                return True

        except Exception as e:
            logger.error(f"连通性测试失败: {e}")
            return False


# 创建默认的异步 API 实例
async_douyin_api = AsyncDouyinAPI()
