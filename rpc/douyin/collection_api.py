"""
抖音收藏相关 API 接口类

专门处理抖音收藏夹和收藏视频相关的 API 调用
所有接口都使用异步客户端（无账户提供者），要求调用端传入 cookies
"""

import asyncio
from typing import Any, Dict, List

import httpx
from loguru import logger
from pydantic import ValidationError

from .client import client_manager
from .config import DouyinConfig
from .exceptions import DouyinResponseError, DouyinValidationError
from .schemas import (
    CollectVideoListRequest,
    CollectVideoListResponse,
    SelfAwemeCollectionRequest,
    SelfAwemeCollectionResponse,
)


class AsyncDouyinCollectionAPI:
    """
    抖音收藏相关异步 API 客户端
    
    专门处理收藏夹和收藏视频相关的 API 调用
    使用异步客户端（无账户提供者），要求调用端传入 cookies
    """

    def __init__(self, config: DouyinConfig = None):
        """
        初始化抖音收藏异步 API 客户端

        Args:
            config: 抖音配置
        """
        self.config = config or DouyinConfig()
        # 使用 create_direct_cookies_async_client 创建异步客户端（无账户提供者）
        self.async_client = client_manager.create_direct_cookies_client(config=self.config)

    def _handle_response(self, response: httpx.Response) -> Dict[str, Any]:
        """
        处理 HTTP 响应

        Args:
            response: httpx 响应对象

        Returns:
            解析后的响应数据

        Raises:
            DouyinResponseError: 响应错误
        """
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP 请求失败: {e}")
            raise DouyinResponseError(f"HTTP 请求失败: {e}", status_code=e.response.status_code)

        try:
            data = response.json()
        except Exception as e:
            logger.error(f"响应数据解析失败: {e}")
            raise DouyinResponseError(f"响应数据解析失败: {e}")

        return data

    async def get_self_aweme_collection(
        self, request: SelfAwemeCollectionRequest, cookies: str
    ) -> SelfAwemeCollectionResponse:
        """
        获取用户收藏夹列表

        Mock: rpc/douyin/tests/mock/get_self_aweme_collection.json

        Args:
            request: 收藏夹列表请求参数
            cookies: 必需的 cookies 字符串

        Returns:
            收藏夹列表响应结果

        Raises:
            DouyinValidationError: 参数验证失败
            DouyinResponseError: 响应错误
        """
        if not cookies:
            raise DouyinValidationError("cookies 参数是必需的")

        url = f"{self.config.base_url}/aweme/v1/web/collects/list/"
        params = request.model_dump(by_alias=True, exclude_none=True)
        headers = {"Cookie": cookies}

        try:
            response = await self.async_client.get(url, params=params, headers=headers)
            data = self._handle_response(response)
            return SelfAwemeCollectionResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_collect_video_list(
        self, request: CollectVideoListRequest, cookies: str
    ) -> CollectVideoListResponse:
        """
        获取收藏夹视频列表

        Mock: rpc/douyin/tests/mock/get_collect_video_list.json

        Args:
            request: 收藏视频列表请求参数
            cookies: 必需的 cookies 字符串

        Returns:
            收藏视频列表响应结果

        Raises:
            DouyinValidationError: 参数验证失败
            DouyinResponseError: 响应错误
        """
        if not cookies:
            raise DouyinValidationError("cookies 参数是必需的")

        url = f"{self.config.base_url}/aweme/v1/web/collects/video/list"
        params = request.model_dump(by_alias=True, exclude_none=True)
        headers = {"Cookie": cookies}

        try:
            response = await self.async_client.get(url, params=params, headers=headers)
            data = self._handle_response(response)
            return CollectVideoListResponse(**data)
        except ValidationError as e:
            raise DouyinValidationError(f"响应数据验证失败: {e}")

    async def get_collect_video_list_with_pagination(
        self,
        collects_id: str,
        cookies: str,
        count: int = 10,
        max_pages: int = 10,
    ) -> List[CollectVideoListResponse]:
        """
        分页获取抖音收藏视频列表

        Mock: rpc/douyin/tests/mock/get_collect_video_list.json (需要创建)

        Args:
            collects_id: 收藏夹ID
            cookies: 必需的 cookies 字符串
            count: 每页数量
            max_pages: 最大页数

        Returns:
            收藏视频列表响应列表

        Raises:
            DouyinValidationError: 参数验证失败
            DouyinResponseError: 响应错误
        """
        if not cookies:
            raise DouyinValidationError("cookies 参数是必需的")

        results = []
        cursor = 0

        for page in range(max_pages):
            try:
                request = CollectVideoListRequest(
                    collects_id=collects_id,
                    cursor=cursor,
                    count=count,
                )

                response = await self.get_collect_video_list(request, cookies)
                results.append(response)

                # 检查是否还有更多数据
                if not response.has_more_videos:
                    break

                # 更新游标
                cursor = response.cursor

                # 添加延迟避免请求过快
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"获取第 {page + 1} 页数据失败: {e}")
                break

        return results

    async def get_aweme_collect_video_list(
        self,
        request: CollectVideoListRequest,
        cookies: str,
    ) -> CollectVideoListResponse:
        """
        获取收藏夹视频列表（别名方法，与现有方法保持一致）

        Mock: rpc/douyin/tests/mock/get_collect_video_list.json (需要创建)

        Args:
            request: 收藏视频列表请求参数
            cookies: 必需的 cookies 字符串

        Returns:
            收藏视频列表响应

        Raises:
            DouyinValidationError: 参数验证失败
            DouyinResponseError: 响应错误
        """
        return await self.get_collect_video_list(request, cookies)

    async def close(self):
        """关闭客户端连接"""
        if self.async_client:
            await self.async_client.aclose()


# 创建全局实例
async_douyin_collection_api = AsyncDouyinCollectionAPI()
