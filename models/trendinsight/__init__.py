"""
TrendInsight 平台模型包

包含 TrendInsight 平台的所有数据模型
"""

from .enums import *
from .models import *

__all__ = [
    # 模型类
    "TrendInsightAuthor",
    "TrendInsightVideo",
    "TrendInsightKeyword",
    "TrendInsightVideoRelated",
    # 工具函数
    "extract_douyin_user_id",
    "update_trendinsight_video",
    "update_trendinsight_author",
    "fetch_and_store_video_trend_score",
    # 枚举类
    "TrendInsightUserType",
    "TrendInsightVideoCategory",
    "TrendInsightAnalysisStatus",
]
