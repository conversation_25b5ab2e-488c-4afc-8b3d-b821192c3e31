"""
抖音平台 Tortoise ORM 模型
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Union

from tortoise import fields

from models.base import BaseModel


def safe_convert_to_timestamp(value: Union[str, int, float, None]) -> int:
    """
    安全地将各种时间格式转换为时间戳

    Args:
        value: 时间值，可以是字符串、整数、浮点数或None

    Returns:
        int: 时间戳（秒）
    """
    if value is None:
        return int(time.time())

    # 如果已经是数字，直接返回
    if isinstance(value, (int, float)):
        # 如果是毫秒时间戳，转换为秒
        if value > 1e12:  # 大于1e12的认为是毫秒时间戳
            return int(value / 1000)
        return int(value)

    # 如果是字符串，尝试解析
    if isinstance(value, str):
        value = value.strip()

        # 如果是纯数字字符串
        if value.isdigit():
            timestamp = int(value)
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                return int(timestamp / 1000)
            return timestamp

        # 尝试解析ISO格式时间字符串
        try:
            # 处理各种ISO格式
            if value.endswith("Z"):
                # UTC时间格式：2025-03-17T08:39:52Z
                dt = datetime.fromisoformat(value.replace("Z", "+00:00"))
            elif "+" in value or value.count("-") > 2:
                # 带时区的格式：2025-03-17T08:39:52+08:00
                dt = datetime.fromisoformat(value)
            else:
                # 本地时间格式：2025-03-17T08:39:52 或 2025-03-17 08:39:52
                value = value.replace(" ", "T")  # 统一格式
                dt = datetime.fromisoformat(value)

            return int(dt.timestamp())

        except (ValueError, TypeError) as e:
            print(f"无法解析时间字符串 '{value}': {e}，使用当前时间")
            return int(time.time())

    # 其他类型，返回当前时间
    print(f"未知的时间格式 '{value}' (类型: {type(value)})，使用当前时间")
    return int(time.time())


def safe_convert_to_datetime(value: Union[str, int, float, None]) -> datetime:
    """
    安全地将各种时间格式转换为 datetime 对象

    Args:
        value: 时间值，可以是字符串、整数、浮点数或None

    Returns:
        datetime: datetime 对象
    """
    if value is None:
        return datetime.now()

    # 如果已经是 datetime 对象，直接返回
    if isinstance(value, datetime):
        return value

    # 如果已经是数字，当作时间戳处理
    if isinstance(value, (int, float)):
        # 如果是毫秒时间戳，转换为秒
        if value > 1e12:  # 大于1e12的认为是毫秒时间戳
            value = value / 1000
        try:
            return datetime.fromtimestamp(value)
        except (ValueError, OSError) as e:
            print(f"无法从时间戳 '{value}' 创建 datetime: {e}，使用当前时间")
            return datetime.now()

    # 如果是字符串，尝试解析
    if isinstance(value, str):
        value = value.strip()

        # 如果是纯数字字符串，当作时间戳处理
        if value.isdigit():
            timestamp = int(value)
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 1e12:
                timestamp = timestamp / 1000
            try:
                return datetime.fromtimestamp(timestamp)
            except (ValueError, OSError) as e:
                print(f"无法从时间戳字符串 '{value}' 创建 datetime: {e}，使用当前时间")
                return datetime.now()

        # 尝试解析ISO格式时间字符串
        try:
            # 处理各种ISO格式
            if value.endswith("Z"):
                # UTC时间格式：2025-03-17T08:39:52Z
                return datetime.fromisoformat(value.replace("Z", "+00:00"))
            elif "+" in value or value.count("-") > 2:
                # 带时区的格式：2025-03-17T08:39:52+08:00
                return datetime.fromisoformat(value)
            else:
                # 本地时间格式：2025-03-17T08:39:52 或 2025-03-17 08:39:52
                value = value.replace(" ", "T")  # 统一格式
                return datetime.fromisoformat(value)

        except (ValueError, TypeError) as e:
            print(f"无法解析时间字符串 '{value}': {e}，使用当前时间")
            return datetime.now()

    # 其他类型，返回当前时间
    print(f"未知的时间格式 '{value}' (类型: {type(value)})，使用当前时间")
    return datetime.now()


class DouyinAweme(BaseModel):
    """抖音视频模型"""

    # 用户信息
    user_id = fields.CharField(max_length=64, null=True, description="用户ID")
    sec_uid = fields.CharField(max_length=128, null=True, description="用户sec_uid")
    short_user_id = fields.CharField(max_length=64, null=True, description="用户短ID")
    user_unique_id = fields.CharField(max_length=64, null=True, description="用户唯一ID")
    nickname = fields.CharField(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=512, null=True, description="用户头像地址")
    user_signature = fields.CharField(max_length=500, null=True, description="用户签名")
    ip_location = fields.CharField(max_length=255, null=True, description="评论时的IP地址")

    # 视频信息
    aweme_id = fields.CharField(max_length=64, unique=True, description="视频ID")
    aweme_type = fields.CharField(max_length=16, description="视频类型")
    title = fields.CharField(max_length=1024, null=True, description="视频标题")
    desc = fields.TextField(null=True, description="视频描述")
    create_time = fields.DatetimeField(description="视频发布时间")

    # 统计信息
    liked_count = fields.CharField(max_length=16, null=True, description="视频点赞数")
    comment_count = fields.CharField(max_length=16, null=True, description="视频评论数")
    share_count = fields.CharField(max_length=16, null=True, description="视频分享数")
    collected_count = fields.CharField(max_length=16, null=True, description="视频收藏数")

    # 媒体信息
    aweme_url = fields.CharField(max_length=500, null=True, description="视频详情页URL")
    cover_url = fields.CharField(max_length=500, null=True, description="视频封面图URL")
    video_download_url = fields.CharField(max_length=1024, null=True, description="视频下载地址")

    # 搜索来源
    source_keyword = fields.CharField(max_length=255, default="", description="搜索来源关键字")

    class Meta:
        table = "douyin_aweme"
        indexes = [
            ("aweme_id",),
            ("create_time",),
            ("user_id",),
            ("sec_uid",),
        ]

    class PydanticMeta:
        computed = ["id"]
        backward_relations = False

    def __str__(self):
        return f"DouyinAweme({self.aweme_id}, {self.title})"


class DouyinAwemeComment(BaseModel):
    """抖音视频评论模型"""

    # 用户信息
    user_id = fields.CharField(max_length=64, null=True, description="用户ID")
    sec_uid = fields.CharField(max_length=128, null=True, description="用户sec_uid")
    short_user_id = fields.CharField(max_length=64, null=True, description="用户短ID")
    user_unique_id = fields.CharField(max_length=64, null=True, description="用户唯一ID")
    nickname = fields.CharField(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=255, null=True, description="用户头像地址")
    user_signature = fields.CharField(max_length=500, null=True, description="用户签名")
    ip_location = fields.CharField(max_length=255, null=True, description="评论时的IP地址")

    # 评论信息
    comment_id = fields.CharField(max_length=64, unique=True, description="评论ID")
    aweme_id = fields.CharField(max_length=64, description="视频ID")
    content = fields.TextField(null=True, description="评论内容")
    create_time = fields.DatetimeField(description="评论时间")
    sub_comment_count = fields.CharField(max_length=16, description="评论回复数")

    # 扩展字段
    parent_comment_id = fields.CharField(max_length=64, null=True, description="父评论ID")
    like_count = fields.CharField(max_length=255, default="0", description="点赞数")
    pictures = fields.CharField(max_length=500, default="", description="评论图片列表")
    reply_to_reply_id = fields.CharField(max_length=64, null=True, description="目标评论ID")

    class Meta:
        table = "douyin_aweme_comment"
        indexes = [
            ("comment_id",),
            ("aweme_id",),
            ("user_id",),
            ("create_time",),
        ]

    def __str__(self):
        return f"DouyinAwemeComment({self.comment_id}, {self.aweme_id})"

    def to_dict(self) -> dict:
        """转换为字典"""
        from datetime import datetime

        result = {}
        for field_name in self._meta.fields:
            value = getattr(self, field_name)
            if isinstance(value, datetime):
                result[field_name] = value.isoformat()
            else:
                result[field_name] = value
        return result


class DouyinCreator(BaseModel):
    """抖音创作者模型"""

    # 用户基本信息
    user_id = fields.CharField(max_length=128, unique=True, description="用户ID")
    nickname = fields.CharField(max_length=64, null=True, description="用户昵称")
    avatar = fields.CharField(max_length=255, null=True, description="用户头像地址")
    ip_location = fields.CharField(max_length=255, null=True, description="评论时的IP地址")

    # 用户详细信息
    desc = fields.TextField(null=True, description="用户描述")
    gender = fields.CharField(max_length=2, null=True, description="性别")

    # 统计信息
    follows = fields.CharField(max_length=16, null=True, description="关注数")
    fans = fields.CharField(max_length=16, null=True, description="粉丝数")
    interaction = fields.CharField(max_length=16, null=True, description="获赞数")

    class Meta:
        table = "douyin_creator"
        indexes = [
            ("user_id",),
            ("nickname",),
        ]

    def __str__(self):
        return f"DouyinCreator({self.user_id}, {self.nickname})"


class DouyinCollect(BaseModel):
    """抖音收藏夹模型"""

    # 基本信息
    app_id = fields.IntField(description="应用ID")
    user_id = fields.BigIntField(description="用户ID")
    user_id_str = fields.CharField(max_length=255, description="用户ID字符串")

    # 收藏夹信息
    collects_id = fields.BigIntField(unique=True, description="收藏夹ID")
    collects_id_str = fields.CharField(max_length=255, description="收藏夹ID字符串")
    collects_name = fields.CharField(max_length=255, description="收藏夹名称")
    collects_cover = fields.CharField(max_length=1024, null=True, description="收藏夹封面")
    sicily_collects_cover = fields.CharField(max_length=1024, null=True, description="Sicily收藏夹封面列表")

    # 状态信息
    follow_status = fields.IntField(default=0, description="关注状态")
    is_normal_status = fields.BooleanField(default=True, description="是否正常状态")
    status = fields.IntField(default=0, description="状态")
    states = fields.IntField(default=0, description="状态值")

    # 统计信息
    followed_count = fields.IntField(default=0, description="关注数")
    play_count = fields.IntField(default=0, description="播放数")
    total_number = fields.IntField(default=0, description="总数量")

    # 类型信息
    item_type = fields.IntField(default=0, description="项目类型")
    system_type = fields.IntField(default=0, description="系统类型")

    # 时间信息
    create_time = fields.DatetimeField(description="创建时间")
    last_collect_time = fields.DatetimeField(description="最后收藏时间")

    class Meta:
        table = "douyin_collect"
        indexes = [
            ("collects_id",),
            ("user_id",),
            ("create_time",),
            ("last_collect_time",),
        ]

    def __str__(self):
        return f"DouyinCollect({self.collects_id}, {self.collects_name})"


class DouyinCollectVideo(BaseModel):
    """抖音收藏夹视频关联模型"""

    # 关联信息
    collects_id = fields.BigIntField(description="收藏夹ID")
    aweme_id = fields.CharField(max_length=64, description="视频ID")

    class Meta:
        table = "douyin_collect_video"
        indexes = [
            ("collects_id",),
            ("aweme_id",),
        ]
        unique_together = [("collects_id", "aweme_id")]  # 联合唯一索引

    def __str__(self):
        return f"DouyinCollectVideo({self.collects_id}, {self.aweme_id})"


# 数据操作函数


def _extract_avatar_url(user_info: dict) -> str:
    """
    提取用户头像URL

    Args:
        user_info (dict): 用户信息字典

    Returns:
        str: 头像URL
    """
    if not user_info:
        return ""

    # 尝试从avatar_thumb获取
    avatar_thumb = user_info.get("avatar_thumb", {})
    if isinstance(avatar_thumb, dict):
        url_list = avatar_thumb.get("url_list", [])
        if url_list and len(url_list) > 0:
            return str(url_list[0])

    # 尝试从avatar_medium获取
    avatar_medium = user_info.get("avatar_medium", {})
    if isinstance(avatar_medium, dict):
        url_list = avatar_medium.get("url_list", [])
        if url_list and len(url_list) > 0:
            return str(url_list[0])

    return ""


def _extract_aweme_detail_url(aweme_item: dict) -> str:
    """
    提取视频详情页URL

    Args:
        aweme_item (dict): 视频数据字典

    Returns:
        str: 视频详情页URL
    """
    if not aweme_item:
        return ""

    aweme_id = aweme_item.get("aweme_id")
    if aweme_id:
        return f"https://www.douyin.com/video/{aweme_id}"

    return ""


def _extract_content_cover_url(aweme_item: dict) -> str:
    """
    提取视频封面URL

    Args:
        aweme_item (dict): 视频数据字典

    Returns:
        str: 视频封面URL
    """
    if not aweme_item:
        return ""

    # 从video字段提取封面
    video_info = aweme_item.get("video", {})
    if isinstance(video_info, dict):
        cover_info = video_info.get("cover", {})
        if isinstance(cover_info, dict):
            url_list = cover_info.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

    return ""


def extract_video_download_url(aweme_detail: dict, by_mobile: bool = False) -> str:
    """
    提取视频下载地址

    Args:
        aweme_detail (dict): 抖音视频详情数据
        by_mobile (bool): 是否为移动端数据，默认为False

    Returns:
        str: 视频下载地址
    """
    # 参数验证：检查 aweme_detail 是否为 None 或空值
    if not aweme_detail:
        return ""

    video_item = aweme_detail.get("video", {})
    url_h264_list = video_item.get("play_addr_h264", {}).get("url_list", [])
    url_256_list = video_item.get("play_addr_256", {}).get("url_list", [])
    url_list = video_item.get("play_addr", {}).get("url_list", [])
    actual_url_list = url_h264_list or url_256_list or url_list

    if not actual_url_list or (not by_mobile and len(actual_url_list) < 2):
        return ""

    return actual_url_list[-1]


async def update_douyin_aweme(aweme_data: Dict[str, Any]) -> bool:
    """
    更新或创建抖音视频数据

    注意：此函数只负责数据库的创建/更新操作，不再处理数据转换。
    数据转换应该在控制器层的 _convert_rpc_to_db_model 方法中完成。

    Args:
        aweme_data: DouyinAweme 模型实例或符合该模型字段的数据字典

    Returns:
        bool: 操作是否成功
    """
    try:
        aweme_id = aweme_data.get("aweme_id")
        if not aweme_id:
            print("update_douyin_aweme: aweme_id 不能为空")
            return False

        # 打印调试信息
        print("save_content_item: ", aweme_data)
        print(f"[models.douyin.update_douyin_aweme] douyin aweme id:{aweme_id}, title:{aweme_data.get('title')}")

        # 使用 update_or_create 方法直接保存已转换的数据
        _, created = await DouyinAweme.update_or_create(aweme_id=aweme_id, defaults=aweme_data)

        action = "创建" if created else "更新"
        print(f"update_douyin_aweme: {action}视频数据成功 - aweme_id: {aweme_id}")
        return True

    except Exception as e:
        print(f"update_douyin_aweme: 操作失败 - {e}")
        return False


async def batch_update_douyin_aweme(aweme_list: List[Dict[str, Any]]) -> bool:
    """
    批量更新或创建抖音视频数据

    注意：此函数只负责数据库的批量创建/更新操作，不再处理数据转换。
    数据转换应该在控制器层完成。

    Args:
        aweme_list: 已经转换好的视频数据字典列表，每个字典的字段应该直接对应DouyinAweme模型

    Returns:
        bool: 操作是否成功
    """
    try:
        if not aweme_list:
            print("batch_update_douyin_aweme: aweme_list 为空")
            return True

        success_count = 0
        for aweme_data in aweme_list:
            if await update_douyin_aweme(aweme_data):
                success_count += 1

        print(f"batch_update_douyin_aweme: 批量处理完成 - 成功: {success_count}/{len(aweme_list)}")
        return success_count > 0

    except Exception as e:
        print(f"batch_update_douyin_aweme: 批量操作失败 - {e}")
        return False
