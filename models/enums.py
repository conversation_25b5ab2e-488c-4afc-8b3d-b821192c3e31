from enum import Enum, StrEnum


class EnumBase(Enum):
    @classmethod
    def get_member_values(cls):
        return [item.value for item in cls._member_map_.values()]

    @classmethod
    def get_member_names(cls):
        return [name for name in cls._member_names_]


class MethodType(StrEnum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


class Platform(StrEnum):
    """平台类型枚举"""

    DOUYIN = "douyin"
    TRENDINSIGHT = "trendinsight"
    XIAOHONGSHU = "xiaohongshu"
    BILIBILI = "bilibili"
    KUAISHOU = "kuaishou"
    WEIBO = "weibo"
    ZHIHU = "zhihu"
    TIEBA = "tieba"


class TaskStatusType(StrEnum):
    """任务状态类型枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ContentType(StrEnum):
    """内容类型枚举"""

    VIDEO = "VIDEO"
    IMAGE = "IMAGE"
    TEXT = "TEXT"
    LIVE = "LIVE"
    STORY = "STORY"


class GenderType(EnumBase):
    """性别类型枚举"""

    UNKNOWN = 0
    MALE = 1
    FEMALE = 2


class AccountStatus(EnumBase):
    """账号状态枚举"""

    NORMAL = 0
    INVALID = -1


class CrawlerType(StrEnum):
    """爬虫类型枚举"""

    SEARCH = "search"
    USER = "user"
    COMMENT = "comment"
    LIVE = "live"


class TaskStatus(StrEnum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class KeywordActionType(StrEnum):
    """关键词操作类型枚举"""

    EXISTING = "existing"
    CREATED = "created"


class SourceType(StrEnum):
    """数据源类型枚举"""

    KEYWORD = "keyword"
    AUTHOR = "author"


class AuthorActionType(StrEnum):
    """作者操作类型枚举"""

    EXISTING = "existing"
    CREATED = "created"
