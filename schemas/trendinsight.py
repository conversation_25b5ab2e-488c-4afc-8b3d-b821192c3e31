"""
TrendInsight API Schemas

定义 TrendInsight API 相关的请求和响应数据格式
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field

from models.enums import AuthorActionType, KeywordActionType


class TrendInsightBaseRequest(BaseModel):
    """TrendInsight 基础请求模型"""

    class Config:
        extra = "allow"


class TrendInsightBaseResponse(BaseModel):
    """TrendInsight 基础响应模型"""

    class Config:
        extra = "allow"


class DarenSearchCreate(BaseModel):
    """达人搜索创建模型"""

    keyword: str = Field(..., description="搜索关键词", example="科技达人")
    page: int = Field(1, description="页码", example=1, ge=1)
    page_size: int = Field(20, description="每页数量", example=20, ge=1, le=100)

    class Config:
        extra = "allow"


class VideoSearchCreate(BaseModel):
    """视频搜索创建模型"""

    keyword: str = Field(..., description="搜索关键词", example="科技")
    author_ids: Optional[List[str]] = Field(None, description="作者ID列表", example=["123456", "789012"])
    category_id: str = Field("0", description="分类ID", example="0")
    date_type: int = Field(0, description="日期类型", example=0)
    label_type: int = Field(0, description="标签类型", example=0)
    duration_type: int = Field(0, description="时长类型", example=0)

    class Config:
        extra = "allow"


class KeywordSyncRequest(BaseModel):
    """关键词同步请求模型"""

    keyword: str = Field(..., description="搜索关键词", example="科技前沿")

    class Config:
        json_schema_extra = {"example": {"keyword": "科技前沿"}}


# 静态定义数据模型，避免动态生成导致的类型检查问题


class KeywordData(BaseModel):
    """关键词数据模型"""

    id: int = Field(..., description="关键词ID")
    keyword: str = Field(..., description="关键词内容")
    keyword_hash: str = Field(..., description="关键词哈希值")
    video_count: int = Field(0, description="视频数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)  # 允许从 ORM 对象创建


class DouyinAwemeData(BaseModel):
    """抖音视频数据模型（用于响应）"""

    # 用户信息
    user_id: Optional[str] = Field(None, description="用户ID", exclude=True)
    sec_uid: Optional[str] = Field(None, description="用户sec_uid", exclude=True)
    short_user_id: Optional[str] = Field(None, description="用户短ID", exclude=True)
    user_unique_id: Optional[str] = Field(None, description="用户唯一ID", exclude=True)
    nickname: Optional[str] = Field(None, description="用户昵称", exclude=True)
    avatar: Optional[str] = Field(None, description="用户头像地址", exclude=True)
    user_signature: Optional[str] = Field(None, description="用户签名", exclude=True)
    ip_location: Optional[str] = Field(None, description="评论时的IP地址", exclude=True)

    # 视频信息
    aweme_id: str = Field(..., description="视频ID")
    aweme_type: str = Field("video", description="视频类型", exclude=True)
    title: Optional[str] = Field(None, description="视频标题", exclude=True)
    desc: Optional[str] = Field(None, description="视频描述", exclude=True)
    create_time: datetime = Field(default_factory=datetime.now, description="视频发布时间")

    # 统计信息
    liked_count: str = Field("0", description="视频点赞数", exclude=True)
    comment_count: str = Field("0", description="视频评论数", exclude=True)
    share_count: str = Field("0", description="视频分享数", exclude=True)
    collected_count: str = Field("0", description="视频收藏数", exclude=True)

    # 媒体信息
    aweme_url: Optional[str] = Field(None, description="视频详情页URL", exclude=True)
    cover_url: Optional[str] = Field(None, description="视频封面图URL", exclude=True)
    video_download_url: Optional[str] = Field(None, description="视频下载地址", exclude=True)

    # 搜索来源
    source_keyword: str = Field("", description="搜索来源关键字", exclude=True)

    # 趋势指数信息
    index: Optional[str] = Field(None, description="趋势指数值", exclude=True)

    model_config = ConfigDict(from_attributes=True)  # 允许从 ORM 对象创建


class AuthorUpdateData(BaseModel):
    """作者更新数据模型（用于创建/更新作者信息）"""

    user_id: str = Field(..., description="TrendInsight用户ID")
    user_name: str = Field("", description="用户名")
    user_head_logo: str = Field("", description="用户头像")
    user_gender: str = Field("", description="用户性别")
    user_location: str = Field("", description="用户位置")
    user_introduction: str = Field("", description="用户简介")

    # 统计数据字段（保留原始字符串）
    item_count: str = Field("0", description="作品数量(原始)")
    fans_count: str = Field("0", description="粉丝数(原始)")
    like_count: str = Field("0", description="点赞数(原始)")

    # 标签字段
    first_tag_name: str = Field("", description="第一标签名称")
    second_tag_name: str = Field("", description="第二标签名称")

    # 粉丝里程碑
    fans_milestone: Optional[dict] = Field(None, description="粉丝里程碑数据")

    # 抖音平台字段
    aweme_id: str = Field("", description="抖音ID")
    user_aweme_url: str = Field("", description="抖音用户链接")
    aweme_pic: str = Field("", description="抖音头像图片")

    # 系统字段
    platform: str = Field("trendinsight", description="来源平台")
    source_keyword: str = Field("", description="搜索来源关键字")
    raw_data: str = Field("", description="原始数据(JSON格式)")

    def to_dict(self) -> dict:
        """转换为字典格式，用于传递给 update_trendinsight_author 函数"""
        return {
            "user_id": self.user_id,
            "user_name": self.user_name,
            "user_head_logo": self.user_head_logo,
            "user_gender": self.user_gender,
            "user_location": self.user_location,
            "user_introduction": self.user_introduction,
            "item_count": self.item_count,
            "fans_count": self.fans_count,
            "like_count": self.like_count,
            "first_tag_name": self.first_tag_name,
            "second_tag_name": self.second_tag_name,
            "fans_milestone": self.fans_milestone or {},
            "aweme_id": self.aweme_id,
            "user_aweme_url": self.user_aweme_url,
            "aweme_pic": self.aweme_pic,
            "platform": self.platform,
            "source_keyword": self.source_keyword,
            "raw_data": self.raw_data,
        }

    @classmethod
    def from_author_detail(cls, author_detail, user_id: str) -> "AuthorUpdateData":
        """从 TrendInsight API 的 AuthorDetailInfo 创建 AuthorUpdateData"""
        # 导入函数以避免循环导入
        from models.trendinsight.models import extract_douyin_user_id

        return cls(
            user_id=user_id,
            user_name=author_detail.user_name or "",
            user_head_logo=author_detail.user_head_logo or "",
            user_gender=author_detail.user_gender or "",
            user_location=author_detail.user_location or "",
            user_introduction=author_detail.user_introduction or "",
            item_count=author_detail.item_count or "0",
            fans_count=author_detail.fans_count or "0",
            like_count=author_detail.like_count or "0",
            first_tag_name=author_detail.first_tag_name or "",
            second_tag_name=author_detail.second_tag_name or "",
            fans_milestone=getattr(author_detail, 'fans_milestone', None),
            aweme_id=author_detail.aweme_id or "",
            user_aweme_url=author_detail.user_aweme_url or "",
            aweme_pic=author_detail.aweme_pic or "",
            platform="trendinsight",
            source_keyword="",
            raw_data="",
        )


class AuthorData(BaseModel):
    """作者数据模型"""

    id: int = Field(..., description="作者ID")
    user_id: str = Field(..., description="TrendInsight用户ID")
    user_name: str = Field(..., description="用户名")
    user_head_logo: Optional[str] = Field(None, description="用户头像")
    user_gender: Optional[str] = Field(None, description="用户性别")
    user_location: Optional[str] = Field(None, description="用户位置")
    user_introduction: Optional[str] = Field(None, description="用户简介")

    # 统计数据字段（保留原始字符串）
    item_count: str = Field(..., description="作品数量(原始)")
    fans_count: str = Field(..., description="粉丝数(原始)")
    like_count: str = Field(..., description="点赞数(原始)")

    # 转换后的数值字段
    item_count_int: int = Field(0, description="作品数量(数值)")
    fans_count_int: int = Field(0, description="粉丝数(数值)")
    like_count_int: int = Field(0, description="点赞数(数值)")

    # 标签分类
    first_tag_name: Optional[str] = Field(None, description="第一标签名称")
    second_tag_name: Optional[str] = Field(None, description="第二标签名称")

    # 粉丝里程碑
    fans_milestone_create_time: Optional[str] = Field(None, description="粉丝里程碑创建时间")

    # 抖音平台字段
    aweme_id: Optional[str] = Field(None, description="抖音ID")
    user_aweme_url: Optional[str] = Field(None, description="抖音用户链接")
    douyin_user_id: Optional[str] = Field(None, description="抖音用户ID")
    aweme_pic: Optional[str] = Field(None, description="抖音头像图片")

    # 系统字段
    platform: str = Field("trendinsight", description="来源平台")
    crawl_time: int = Field(..., description="爬取时间戳")
    source_keyword: str = Field("", description="搜索来源关键字")

    # 扩展字段
    raw_data: Optional[str] = Field(None, description="原始数据(JSON格式)")

    # 时间字段
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    model_config = ConfigDict(from_attributes=True)  # 允许从 ORM 对象创建


class KeywordSyncResponse(BaseModel):
    """关键词同步响应模型"""

    keyword_action: KeywordActionType = Field(
        ..., description="关键词操作类型：existing（已存在）、created（新创建）", example=KeywordActionType.CREATED
    )
    keyword_data: Optional[KeywordData] = Field(None, description="关键词基本数据")
    videos_synced: int = Field(..., description="同步的视频数量", example=25)
    videos_failed: int = Field(..., description="同步失败的视频数量", example=0)
    relations_created: int = Field(..., description="新创建的关联记录数量", example=20)
    relations_existing: int = Field(..., description="已存在的关联记录数量", example=5)
    video_items: List[DouyinAwemeData] = Field(
        default_factory=list, description="视频项目列表（包含详细信息）", example=[]
    )
    errors: List[str] = Field(..., description="错误信息列表", example=[])

    class Config:
        json_schema_extra = {
            "example": {
                "keyword_action": KeywordActionType.CREATED,
                "keyword_data": {
                    "id": 123,
                    "keyword": "科技前沿",
                    "keyword_hash": "abc123def456",
                    "video_count": 25,
                },
                "videos_synced": 25,
                "videos_failed": 0,
                "relations_created": 20,
                "relations_existing": 5,
                "errors": [],
            }
        }


class AuthorSyncRequest(BaseModel):
    """作者同步请求模型"""

    user_id: str = Field(..., description="TrendInsight 用户ID", example="heicgcbajggjdjjaefj")

    class Config:
        json_schema_extra = {"example": {"user_id": "heicgcbajggjdjjaefj"}}


class AuthorSyncResponse(BaseModel):
    """作者同步响应模型"""

    author_action: AuthorActionType = Field(
        ..., description="作者操作类型：existing（已存在）、created（新创建）", example=AuthorActionType.CREATED
    )
    author_data: Optional[AuthorData] = Field(None, description="作者基本数据")
    videos_synced: int = Field(..., description="同步的视频数量", example=15)
    videos_failed: int = Field(..., description="同步失败的视频数量", example=0)
    relations_created: int = Field(..., description="新创建的关联记录数量", example=15)
    relations_existing: int = Field(..., description="已存在的关联记录数量", example=0)
    aweme_ids: List[str] = Field(
        ..., description="获取到的所有视频aweme_id列表", example=["7123456789012345678", "7123456789012345679"]
    )
    video_items: List[DouyinAwemeData] = Field(
        default_factory=list, description="视频项目列表（包含详细信息）", example=[]
    )
    errors: List[str] = Field(..., description="错误信息列表", example=[])

    class Config:
        json_schema_extra = {
            "example": {
                "author_action": AuthorActionType.CREATED,
                "author_data": {
                    "user_id": "heicgcbajggjdjjaefj",
                    "user_name": "科技博主",
                    "douyin_user_id": "MS4wLjABAAAA123",
                },
                "videos_synced": 15,
                "videos_failed": 0,
                "relations_created": 15,
                "relations_existing": 0,
                "aweme_ids": ["7123456789012345678", "7123456789012345679", "7123456789012345680"],
                "errors": [],
            }
        }


class VideoIndexCreate(BaseModel):
    """视频指数查询创建模型"""

    item_id: str = Field(..., description="视频ID", example="7123456789012345678")
    start_date: str = Field(..., description="开始日期，格式：YYYYMMDD", example="20250101")
    end_date: str = Field(..., description="结束日期，格式：YYYYMMDD", example="20250107")

    class Config:
        extra = "allow"


class AuthorDetailCreate(BaseModel):
    """作者详情查询创建模型"""

    user_id: str = Field(..., description="用户ID", example="123456789")

    class Config:
        extra = "allow"


class VideoTrendData(BaseModel):
    """视频趋势数据模型"""

    aweme_id: str = Field(..., description="视频ID", example="7123456789012345678")
    trend_score: float = Field(..., description="趋势评分", example=85.6)
    created_at: str = Field(..., description="创建时间", example="2024-01-01T12:00:00")
    updated_at: str = Field(..., description="更新时间", example="2024-01-01T12:00:00")
    is_deleted: bool = Field(False, description="是否已删除", example=False)


class VideoTrendSyncResponse(BaseModel):
    """视频趋势同步响应模型（用于 sync-trend 端点）"""

    success: bool = Field(..., description="操作是否成功", example=True)
    message: str = Field(..., description="操作结果消息", example="成功同步趋势数据")
    data: Optional[VideoTrendData] = Field(None, description="视频趋势数据（成功时返回）")
    error: Optional[str] = Field(None, description="错误信息（失败时返回）")

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "summary": "同步成功",
                    "value": {
                        "success": True,
                        "message": "成功同步趋势数据",
                        "data": {
                            "aweme_id": "7123456789012345678",
                            "trend_score": 85.6,
                            "created_at": "2024-01-01T12:00:00",
                            "updated_at": "2024-01-01T12:00:00",
                            "is_deleted": False
                        }
                    }
                },
                {
                    "summary": "同步失败",
                    "value": {
                        "success": False,
                        "message": "同步趋势数据失败",
                        "error": "可能的原因：cookies无效、视频不存在、网络问题或API限流"
                    }
                },
                {
                    "summary": "数据查询失败",
                    "value": {
                        "success": False,
                        "message": "数据同步成功但查询失败",
                        "error": "视频记录未找到"
                    }
                }
            ]
        }


# 对于响应模型，我们直接使用 RPC 客户端中定义的模型
# 这样可以保持一致性，避免重复定义

# 重新导出 RPC 模型作为 API 响应模型
from rpc.trendinsight.schemas import (
    AuthorDetailResponse,
    DarenSearchRequest,
    DarenSearchResponse,
    TrendInsightSignRequest,
    TrendInsightSignResponse,
    UserInfoResponse,
    VideoIndexResponse,
    VideoSearchResponse,
)

__all__ = [
    # Request models
    "TrendInsightBaseRequest",
    "DarenSearchCreate",
    "VideoSearchCreate",
    "VideoIndexCreate",
    "AuthorDetailCreate",
    "KeywordSyncRequest",
    "AuthorSyncRequest",
    # Internal models
    "KeywordData",
    "AuthorData",
    "AuthorUpdateData",
    "VideoTrendData",
    # Response models (从 RPC 导入)
    "TrendInsightBaseResponse",
    "KeywordSyncResponse",
    "AuthorSyncResponse",
    "VideoTrendSyncResponse",
    "UserInfoResponse",
    "DarenSearchRequest",
    "DarenSearchResponse",
    "VideoSearchResponse",
    "AuthorDetailResponse",
    "VideoIndexResponse",
    "TrendInsightSignRequest",
    "TrendInsightSignResponse",
]
