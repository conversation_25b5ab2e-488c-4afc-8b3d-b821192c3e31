"""
FastAPI 应用入口

初始化 FastAPI 应用和相关配置
"""

from fastapi import FastAPI
from tortoise.contrib.fastapi import register_tortoise

from core.init_app import (
    init_data,
    make_middlewares,
    register_exceptions,
    register_routers,
)
from core.openapi_config import setup_openapi_docs
from settings.config import settings
from utils.cache_manager import start_cache_cleanup_task

# Validate settings before app starts
settings.validators.validate()

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.APP_TITLE,
    description=settings.APP_DESCRIPTION,
    version=settings.VERSION,
    middleware=make_middlewares(),
)

# 注册异常处理器
register_exceptions(app)

# 注册路由
register_routers(app)

# 设置 OpenAPI 文档配置
setup_openapi_docs(app)

# 注册 Tortoise ORM
register_tortoise(
    app,
    config=settings.TORTOISE_ORM,
    generate_schemas=False,  # 重新启用自动表创建
    add_exception_handlers=True,
)


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行的初始化操作"""
    await init_data()
    # 启动缓存清理任务
    start_cache_cleanup_task()


# 根路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "Vue FastAPI Admin Backend", "version": settings.VERSION, "docs": "/docs", "redoc": "/redoc"}


# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}
