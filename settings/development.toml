# 开发环境配置
[development]
database_uri = "postgresql://user:pass@localhost:5432/devdb"
log_level = "DEBUG"
debug = true
secret_key = "dev-secret-key-change-in-production"
kuaidaili_dps_order_id = "your-dev-order-id-here"

# Tortoise ORM 配置 - 开发环境
tortoise_orm = { connections = { default = "sqlite://db.sqlite3" }, apps = { models = { models = ["models", "aerich.models"], default_connection = "default" } } }

# 开发环境特定配置
app_title = "Vue FastAPI Admin - Development"
project_name = "Vue FastAPI Admin - Dev"

# 开发环境允许更宽松的CORS设置
cors_origins = ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"]

# 开发环境JWT过期时间更长
jwt_access_token_expire_minutes = 43200  # 30 days