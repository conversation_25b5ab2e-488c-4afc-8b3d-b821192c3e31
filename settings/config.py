import os

from dynaconf import Dynaconf, Validator

# Project root directory
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))

settings = Dynaconf(
    envvar_prefix="APP",
    settings_files=[
        "settings/default.toml",
        "settings/development.toml",
        "settings/staging.toml",
        "settings/production.toml",
        ".secrets.toml",
    ],
    environments=True,
    load_dotenv=True,
    # Add dynamic values to settings
    PROJECT_ROOT=PROJECT_ROOT,
    BASE_DIR=PROJECT_ROOT,
    LOGS_ROOT=os.path.join(PROJECT_ROOT, "logs"),
)

# Validation
settings.validators.register(
    # General App Settings
    Validator("version", default="0.1.0"),
    Validator("app_title", default="Vue FastAPI Admin"),
    Validator("project_name", default="Vue FastAPI Admin"),
    Validator("app_description", default="Description"),
    Validator("debug", default=True, is_type_of=bool),
    # CORS Settings
    Validator("cors_origins", default=["*"], is_type_of=list),
    Validator("cors_allow_credentials", default=True, is_type_of=bool),
    Validator("cors_allow_methods", default=["*"], is_type_of=list),
    Validator("cors_allow_headers", default=["*"], is_type_of=list),
    # Security Settings
    Validator("secret_key", must_exist=True, is_type_of=str),
    Validator("jwt_algorithm", default="HS256"),
    Validator("jwt_access_token_expire_minutes", default=60 * 24 * 7, is_type_of=int),
    # Database Settings (tortoise_orm)
    Validator("tortoise_orm.connections.default", must_exist=True),
    Validator("tortoise_orm.apps.models.models", default=["models", "aerich.models"]),
    Validator("tortoise_orm.apps.models.default_connection", default="default"),
    # Datetime format
    Validator("datetime_format", default="%Y-%m-%d %H:%M:%S"),
    # Kuaidaili settings
    Validator("kuaidaili_dps_secret_id", must_exist=True),
    Validator("kuaidaili_dps_signature", must_exist=True),
    Validator("kuaidaili_api_base_url", default="https://dps.kuaidaili.com"),
    Validator("kuaidaili_username", default=""),
    Validator("kuaidaili_password", default=""),
)

# It is recommended to call validate() at the application's entry point (e.g., in main.py)
# to ensure all modules are loaded before validation.
# settings.validators.validate()
