# 生产环境配置
[production]
database_uri = "env_var_for_prod_db_uri"
log_level = "WARNING"
debug = false
secret_key = "prod-secret-key-change-this"
kuaidaili_dps_order_id = "your-prod-order-id-here"

# Tortoise ORM 配置 - 生产环境
tortoise_orm = { connections = { default = "@format {this.database_uri}" }, apps = { models = { models = ["models", "aerich.models"], default_connection = "default" } } }

# 生产环境特定配置
app_title = "Vue FastAPI Admin"
project_name = "Vue FastAPI Admin"

# 生产环境严格的CORS设置
cors_origins = ["https://yourdomain.com", "https://www.yourdomain.com"]

# 生产环境JWT过期时间
jwt_access_token_expire_minutes = 1440  # 24 hours