#!/bin/bash

# 启动脚本 - 独立Python后端项目
# 使用方法: ./start.sh

echo "正在启动 Python 后端服务..."
echo "项目目录: $(pwd)"
echo "Python 版本: $(python --version)"
echo ""

# 检查依赖是否已安装
if [ ! -d "venv" ] && [ ! -f ".venv/pyvenv.cfg" ]; then
    echo "建议创建虚拟环境:"
    echo "python -m venv venv"
    echo "source venv/bin/activate  # Linux/Mac"
    echo "# 或者"
    echo "venv\\Scripts\\activate  # Windows"
    echo ""
fi

# 检查requirements.txt
if [ -f "requirements.txt" ]; then
    echo "依赖文件: requirements.txt 存在"
    echo "如需安装依赖，请运行: pip install -r requirements.txt"
    echo ""
fi

# 启动服务器
echo "启动 FastAPI 服务器..."
echo "访问地址: http://localhost:8000"
echo "API 文档: http://localhost:8000/docs"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "================================"

python run.py
