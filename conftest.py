"""
pytest 全局配置文件

定义整个项目的测试环境配置、fixtures和钩子函数
"""

import os

import pytest


def pytest_configure(config):
    """pytest 配置钩子"""
    # 检查是否在CI环境中
    is_ci = os.getenv("CI", "").lower() in ("true", "1", "yes")

    if is_ci:
        print("🤖 检测到CI环境，将跳过集成测试")
        # 在CI环境中自动添加 -m "not ci_skip" 标记
        if not config.getoption("-m"):
            config.option.markexpr = "not ci_skip"
    else:
        print("💻 本地开发环境，可运行所有测试")


@pytest.fixture(scope="session")
def test_environment():
    """测试环境信息"""
    return {
        "is_ci": os.getenv("CI", "").lower() in ("true", "1", "yes"),
        "is_local": os.getenv("CI", "").lower() not in ("true", "1", "yes"),
        "python_version": os.getenv("PYTHON_VERSION", "unknown"),
        "test_mode": os.getenv("TEST_MODE", "unit"),
    }


@pytest.fixture(scope="session")
def integration_test_config():
    """集成测试配置"""
    return {
        "enable_real_api": os.getenv("ENABLE_REAL_API_TESTS", "false").lower() == "true",
        "api_timeout": int(os.getenv("API_TEST_TIMEOUT", "30")),
        "max_retries": int(os.getenv("API_TEST_RETRIES", "2")),
        "test_cookies": os.getenv("DOUYIN_TEST_COOKIES", ""),
        "test_video_id": os.getenv("TEST_VIDEO_ID", "7509429111140420874"),
        "test_user_id": os.getenv(
            "TEST_USER_ID", "MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP"
        ),
    }


@pytest.fixture
def skip_if_ci():
    """在CI环境中跳过测试的装饰器"""
    is_ci = os.getenv("CI", "").lower() in ("true", "1", "yes")
    if is_ci:
        pytest.skip("跳过CI环境中的集成测试")


@pytest.fixture
def require_real_api():
    """需要真实API的测试装饰器"""
    enable_real_api = os.getenv("ENABLE_REAL_API_TESTS", "false").lower() == "true"
    if not enable_real_api:
        pytest.skip("真实API测试被禁用，设置 ENABLE_REAL_API_TESTS=true 启用")


# 测试报告钩子
def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """测试结束后的总结报告"""
    if hasattr(terminalreporter, "stats"):
        stats = terminalreporter.stats

        # 统计不同类型的测试
        total_tests = len(stats.get("passed", [])) + len(stats.get("failed", [])) + len(stats.get("skipped", []))
        integration_tests = 0
        unit_tests = 0

        for test_list in [stats.get("passed", []), stats.get("failed", []), stats.get("skipped", [])]:
            for test in test_list:
                if hasattr(test, "keywords"):
                    if "integration" in test.keywords:
                        integration_tests += 1
                    elif "unit" in test.keywords:
                        unit_tests += 1

        print("\n📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   单元测试: {unit_tests}")
        print(f"   集成测试: {integration_tests}")

        is_ci = os.getenv("CI", "").lower() in ("true", "1", "yes")
        if is_ci:
            print("   🤖 CI环境: 已跳过集成测试")
        else:
            print("   💻 本地环境: 包含所有测试类型")


# 测试失败时的额外信息
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """生成测试报告时的钩子"""
    outcome = yield
    rep = outcome.get_result()

    # 为集成测试失败添加额外信息
    if rep.when == "call" and rep.failed:
        if "integration" in item.keywords or "real_api" in item.keywords:
            rep.longrepr.addsection(
                "集成测试失败提示",
                "这是一个集成测试失败。可能的原因:\n"
                "1. 网络连接问题\n"
                "2. API服务不可用\n"
                "3. 认证信息过期\n"
                "4. 请求参数变化\n"
                "建议检查网络连接和API服务状态",
            )


# 环境变量验证
def pytest_sessionstart(session):
    """测试会话开始时的钩子"""
    is_ci = os.getenv("CI", "").lower() in ("true", "1", "yes")

    if not is_ci:
        # 本地环境检查
        print("🔍 检查本地测试环境...")

        # 检查是否有测试用的cookies
        test_cookies = os.getenv("DOUYIN_TEST_COOKIES", "")
        if not test_cookies:
            print("⚠️  未设置 DOUYIN_TEST_COOKIES 环境变量")
            print("   集成测试可能会失败，建议设置有效的cookies")

        # 检查是否启用真实API测试
        enable_real_api = os.getenv("ENABLE_REAL_API_TESTS", "false").lower() == "true"
        if enable_real_api:
            print("✅ 真实API测试已启用")
        else:
            print("ℹ️  真实API测试未启用，设置 ENABLE_REAL_API_TESTS=true 启用")
    else:
        print("🤖 CI环境检测完成，集成测试将被跳过")


# 自定义pytest命令行选项
def pytest_addoption(parser):
    """添加自定义命令行选项"""
    parser.addoption("--run-integration", action="store_true", default=False, help="强制运行集成测试（即使在CI环境中）")

    parser.addoption("--real-api", action="store_true", default=False, help="运行真实API测试")

    parser.addoption("--unit-only", action="store_true", default=False, help="只运行单元测试")


def pytest_collection_modifyitems(config, items):
    """根据命令行选项修改测试收集"""
    # 为集成测试添加慢速标记
    for item in items:
        if "integration" in item.keywords or "real_api" in item.keywords:
            item.add_marker(pytest.mark.slow)

    # 根据命令行选项处理
    if config.getoption("--unit-only"):
        # 只运行单元测试
        skip_integration = pytest.mark.skip(reason="--unit-only 选项：跳过集成测试")
        for item in items:
            if "integration" in item.keywords or "real_api" in item.keywords:
                item.add_marker(skip_integration)

    elif config.getoption("--real-api"):
        # 强制运行真实API测试
        os.environ["ENABLE_REAL_API_TESTS"] = "true"

    elif config.getoption("--run-integration"):
        # 强制运行集成测试（覆盖CI检测）
        pass
    else:
        # 默认行为：CI环境跳过集成测试
        is_ci = os.getenv("CI", "").lower() in ("true", "1", "yes")
        if is_ci:
            skip_ci = pytest.mark.skip(reason="CI环境：跳过集成测试")
            for item in items:
                if "ci_skip" in item.keywords:
                    item.add_marker(skip_ci)
