"""
代理模块使用示例

展示如何使用重构后的代理模块，包括基本使用、高级配置和最佳实践。
"""

import asyncio
import logging

# 导入代理模块
from pkg.proxy import create_ip_pool, create_kuaidaili_pool

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def basic_usage_example():
    """基本使用示例"""
    print("=== 基本使用示例 ===")

    # 创建代理池（使用工厂函数）
    pool = create_ip_pool(
        provider_type="kuaidaili",
        api_key="your_api_key_here",  # 替换为实际的API密钥
        api_secret="your_api_secret_here",  # 替换为实际的API密码
    )

    try:
        # 加载代理
        print("正在加载代理...")
        proxies = await pool.load_proxies(count=5)
        print(f"成功加载 {len(proxies)} 个代理")

        # 获取代理
        for i in range(3):
            proxy = await pool.get_proxy()
            if proxy:
                print(f"代理 {i+1}: {proxy.to_proxy_url()}")
            else:
                print(f"代理 {i+1}: 无可用代理")

        # 显示池状态
        status = await pool.get_pool_status()
        print(f"代理池状态: {status}")

    except Exception as e:
        print(f"错误: {e}")
    finally:
        # 清理资源
        await pool.close()


async def advanced_configuration_example():
    """高级配置示例"""
    print("\n=== 高级配置示例 ===")

    # 使用便捷工厂函数创建快代理池
    pool = create_kuaidaili_pool(
        api_key="your_api_key_here", api_secret="your_api_secret_here", cache_ttl=7200  # 缓存2小时
    )

    try:
        # 加载更多代理
        print("正在加载代理...")
        proxies = await pool.load_proxies(count=10)
        print(f"成功加载 {len(proxies)} 个代理")

        # 批量获取代理
        print("批量获取代理:")
        proxy_batch = await pool.get_proxies(count=3)
        for i, proxy in enumerate(proxy_batch):
            print(f"  代理 {i+1}: {proxy.to_proxy_url()}")

        # 刷新代理池
        print("刷新代理池...")
        await pool.refresh_proxies(count=5)

        # 清除缓存
        print("清除缓存...")
        await pool.clear_cache()

    except Exception as e:
        print(f"错误: {e}")
    finally:
        await pool.close()


async def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")

    # 创建一个配置错误的代理池
    try:
        pool = create_ip_pool(provider_type="invalid_provider", api_key="invalid_key")
    except ValueError as e:
        print(f"配置错误: {e}")

    # 创建正确的代理池但使用无效凭据
    pool = create_ip_pool(provider_type="kuaidaili", api_key="invalid_key", api_secret="invalid_secret")

    try:
        # 尝试加载代理
        await pool.load_proxies(count=5)
    except Exception as e:
        print(f"加载代理失败: {e}")
    finally:
        await pool.close()


async def performance_example():
    """性能优化示例"""
    print("\n=== 性能优化示例 ===")

    # 创建高性能配置的代理池
    pool = create_kuaidaili_pool(
        api_key="your_api_key_here", api_secret="your_api_secret_here", cache_ttl=3600  # 1小时缓存
    )

    try:
        # 预加载大量代理
        print("预加载代理...")
        start_time = asyncio.get_event_loop().time()
        proxies = await pool.load_proxies(count=20)
        load_time = asyncio.get_event_loop().time() - start_time
        print(f"加载 {len(proxies)} 个代理耗时: {load_time:.2f} 秒")

        # 快速获取代理
        print("快速获取代理:")
        start_time = asyncio.get_event_loop().time()
        for i in range(10):
            proxy = await pool.get_proxy()
            if proxy:
                print(f"  代理 {i+1}: {proxy.ip}:{proxy.port}")
        get_time = asyncio.get_event_loop().time() - start_time
        print(f"获取10个代理耗时: {get_time:.2f} 秒")

    except Exception as e:
        print(f"错误: {e}")
    finally:
        await pool.close()


async def main():
    """主函数，运行所有示例"""
    print("代理模块使用示例")
    print("=" * 50)

    # 注意：在实际使用中，请替换为真实的API密钥
    print("注意：请在运行前替换示例中的API密钥为真实值")
    print()

    try:
        # 运行基本示例
        await basic_usage_example()

        # 运行高级配置示例
        await advanced_configuration_example()

        # 运行错误处理示例
        await error_handling_example()

        # 运行性能示例
        await performance_example()

    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        logger.exception("详细错误信息:")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
