"""
爬虫账号管理器模块

提供爬虫账号的获取、状态管理功能。

主要功能：
1. 获取可用账号：从数据库中获取指定平台的可用账号
2. 标记账号不可用：将账号设置为不可用状态
3. 账号共享：支持多个进程/线程共享使用同一个账号

使用示例：
    from pkg.crawler_account import AccountManager, NoAvailableAccountError

    # 创建账号管理器
    manager = AccountManager()

    # 获取可用账号
    try:
        account = await manager.get_available_account("douyin")
        print(f"获取到账号: {account.account_name}")

        # 使用完成 - 无需释放账号
        print("账号使用完成")

    except NoAvailableAccountError:
        print("没有可用账号")
"""

# 模块导入

try:
    from .account_manager import AccountManager
    from .exceptions import (
        AccountNotFoundError,
        CrawlerAccountError,
        DatabaseOperationError,
        NoAvailableAccountError,
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from account_manager import AccountManager

    from pkg.crawler_account.exceptions import (
        AccountNotFoundError,
        CrawlerAccountError,
        DatabaseOperationError,
        NoAvailableAccountError,
    )

# 导出的公共接口
__all__ = [
    # 核心类
    "AccountManager",
    # 异常类
    "CrawlerAccountError",
    "NoAvailableAccountError",
    "AccountNotFoundError",
    "DatabaseOperationError",
]

# 版本信息
__version__ = "1.0.0"
__author__ = "MediaCrawler Team"
__description__ = "爬虫账号管理器模块"
