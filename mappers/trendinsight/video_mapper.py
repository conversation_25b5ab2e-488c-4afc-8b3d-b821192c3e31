"""
TrendInsight 视频数据映射器

负责将 TrendInsight API 返回的视频数据转换为内部数据模型。
提供两种转换方法：
1. 视频对象转换：处理作者搜索返回的视频数据
2. 关键词视频对象转换：处理关键词搜索返回的视频数据
"""

from datetime import datetime
from typing import TYPE_CHECKING, Any, List, Optional

from models.enums import ContentType

if TYPE_CHECKING:
    from rpc.trendinsight.schemas.video import VideoInfo
    from rpc.trendinsight.schemas.daren import TopVideoInfo
    from schemas.trendinsight import DouyinAwemeData
    from models.douyin.models import DouyinAweme


class TrendInsightVideoMapper:
    """TrendInsight 视频数据映射器
    
    提供两种视频数据转换方法：
    1. 视频对象转换：处理作者搜索返回的视频数据
    2. 关键词视频对象转换：处理关键词搜索返回的视频数据
    """

    @staticmethod
    def _extract_aweme_detail_url(aweme_id: str) -> str:
        """
        根据 aweme_id 生成抖音视频详情页URL

        Args:
            aweme_id: 抖音视频ID

        Returns:
            str: 视频详情页URL
        """
        if aweme_id:
            return f"https://www.douyin.com/video/{aweme_id}"
        return ""

    @staticmethod
    def _parse_timestamp(create_time_str: Optional[str]) -> int:
        """
        解析时间戳字符串

        Args:
            create_time_str: 时间字符串，可能是 "YYYY-MM-DD HH:MM:SS" 格式或时间戳数字

        Returns:
            int: Unix 时间戳，解析失败时返回 0
        """
        if not create_time_str:
            return 0

        try:
            # 尝试解析时间字符串（假设格式为 "YYYY-MM-DD HH:MM:SS"）
            dt = datetime.strptime(create_time_str, "%Y-%m-%d %H:%M:%S")
            return int(dt.timestamp())
        except (ValueError, TypeError):
            # 如果解析失败，尝试将其作为数字处理
            try:
                return int(create_time_str)
            except (ValueError, TypeError):
                return 0

    @staticmethod
    def _parse_to_datetime(create_time_str: Optional[str]) -> datetime:
        """
        解析时间字符串为 datetime 对象

        Args:
            create_time_str: 时间字符串，可能是 "YYYY-MM-DD HH:MM:SS" 格式或时间戳数字

        Returns:
            datetime: datetime 对象，解析失败时返回当前时间
        """
        if not create_time_str:
            return datetime.now()

        try:
            # 尝试解析时间字符串（假设格式为 "YYYY-MM-DD HH:MM:SS"）
            return datetime.strptime(create_time_str, "%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            # 如果解析失败，尝试将其作为时间戳处理
            try:
                timestamp = int(create_time_str)
                # 如果是毫秒时间戳，转换为秒
                if timestamp > 1e12:
                    timestamp = timestamp / 1000
                return datetime.fromtimestamp(timestamp)
            except (ValueError, TypeError, OSError):
                return datetime.now()

    @staticmethod
    def video_to_douyin_aweme_data(video: Any, source_keyword: str) -> "DouyinAwemeData":
        """
        将 TrendInsight 视频对象转换为 DouyinAwemeData

        Args:
            video: TrendInsight API 返回的视频对象
            source_keyword: 搜索来源关键字

        Returns:
            DouyinAwemeData: 转换后的视频数据模型

        Raises:
            AttributeError: 当视频对象缺少必要的 item_id 字段时抛出
        """
        if not hasattr(video, "item_id") or not video.item_id:
            raise AttributeError("视频对象缺少必要的 item_id 字段")

        # 解析发布时间
        create_time_str: Optional[str] = getattr(video, "create_time", None)
        publish_time: datetime = TrendInsightVideoMapper._parse_to_datetime(create_time_str)

        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        return DouyinAwemeData(
            # 用户信息
            user_id=getattr(video, "author_user_id", None),
            sec_uid=getattr(video, "sec_uid", None),
            short_user_id=getattr(video, "short_user_id", None),
            user_unique_id=getattr(video, "user_unique_id", None),
            nickname=getattr(video, "author_nickname", None),
            avatar=getattr(video, "avatar", None),
            user_signature=getattr(video, "user_signature", None),
            ip_location=getattr(video, "ip_location", None),
            # 视频信息
            aweme_id=video.item_id,
            aweme_type=getattr(video, "aweme_type", ContentType.VIDEO.value),
            title=getattr(video, "title", None)[:1000] if getattr(video, "title", None) else None,
            desc=getattr(video, "desc", None)[:1000] if getattr(video, "desc", None) else None,
            create_time=publish_time,
            # 统计信息
            liked_count=getattr(video, "liked_count", "0"),
            comment_count=getattr(video, "comment_count", "0"),
            share_count=getattr(video, "share_count", "0"),
            collected_count=getattr(video, "collected_count", "0"),
            # 媒体信息
            aweme_url=getattr(video, "aweme_url", None) or TrendInsightVideoMapper._extract_aweme_detail_url(video.item_id),
            cover_url=getattr(video, "cover_url", None),
            video_download_url=getattr(video, "video_download_url", None),
            # 搜索来源
            source_keyword=source_keyword,
        )

    @staticmethod
    def keyword_video_to_douyin_aweme_data(video: "VideoInfo", source_keyword: str) -> "DouyinAwemeData":
        """
        将 TrendInsight 关键词搜索返回的视频对象转换为 DouyinAwemeData

        Args:
            video: TrendInsight API 关键词搜索返回的视频对象
            source_keyword: 搜索来源关键字

        Returns:
            DouyinAwemeData: 转换后的视频数据模型

        Raises:
            AttributeError: 当视频对象缺少必要的 item_id 字段时抛出
        """
        # 使用强类型访问而不是 getattr
        item_id = video.item_id
        if not item_id:
            raise AttributeError("视频对象缺少必要的 item_id 字段")

        # 解析发布时间
        create_time_str: Optional[str] = video.create_time
        publish_time: datetime = TrendInsightVideoMapper._parse_to_datetime(create_time_str)

        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        return DouyinAwemeData(
            # 用户信息
            user_id=None,  # 关键词搜索数据中没有用户ID
            sec_uid=None,  # 关键词搜索数据中没有sec_uid
            short_user_id=None,  # 关键词搜索数据中没有short_user_id
            user_unique_id=None,  # 关键词搜索数据中没有user_unique_id
            nickname=video.nickname,
            avatar=video.avatar_url,
            user_signature=None,  # 关键词搜索数据中没有用户签名
            ip_location=None,  # 关键词搜索数据中没有IP位置
            # 视频信息
            aweme_id=item_id,
            aweme_type=ContentType.VIDEO.value,  # 默认视频类型
            title=video.title[:1000] if video.title else None,
            desc=video.title,
            create_time=publish_time,
            # 统计信息
            liked_count=video.likes or "0",
            comment_count="0",  # 关键词搜索数据中没有评论数
            share_count="0",  # 关键词搜索数据中没有分享数
            collected_count="0",  # 关键词搜索数据中没有收藏数
            # 媒体信息
            aweme_url=TrendInsightVideoMapper._extract_aweme_detail_url(item_id),
            cover_url=video.thumbnail,
            video_download_url=None,  # 关键词搜索数据中没有视频下载地址
            # 搜索来源
            source_keyword=source_keyword,
            # 趋势指数信息
            index=video.index,
        )

    @staticmethod
    def videos_to_douyin_aweme_data_list(
        videos: list, source_keyword: str
    ) -> tuple[list["DouyinAwemeData"], list[str]]:
        """
        批量将 TrendInsight 视频对象列表转换为 DouyinAwemeData 列表

        Args:
            videos: TrendInsight API 返回的视频对象列表 (List[Any])
            source_keyword: 搜索来源关键字

        Returns:
            tuple: (转换成功的 DouyinAwemeData 列表, 视频ID列表)
        """
        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        video_items: list[DouyinAwemeData] = []
        video_ids: list[str] = []

        for video in videos:
            try:
                # 跳过没有 item_id 的视频
                if not hasattr(video, "item_id") or not video.item_id:
                    continue

                # 转换视频数据
                video_item = TrendInsightVideoMapper.video_to_douyin_aweme_data(video, source_keyword)
                video_items.append(video_item)
                video_ids.append(video.item_id)

            except (AttributeError, Exception):
                # 记录错误但继续处理其他视频
                # 这里可以添加日志记录
                continue

        return video_items, video_ids

    @staticmethod
    def keyword_videos_to_douyin_aweme_data_list(
        videos: List["VideoInfo"], source_keyword: str
    ) -> tuple[list["DouyinAwemeData"], list[str]]:
        """
        批量将 TrendInsight 关键词搜索返回的视频对象列表转换为 DouyinAwemeData 列表

        Args:
            videos: TrendInsight API 关键词搜索返回的视频对象列表 (List[VideoInfo])
            source_keyword: 搜索来源关键字

        Returns:
            tuple: (转换成功的 DouyinAwemeData 列表, 视频ID列表)
        """
        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        video_items: list[DouyinAwemeData] = []
        video_ids: list[str] = []

        for video in videos:
            try:
                # 使用强类型访问而不是 getattr
                item_id = video.item_id
                if not item_id:
                    continue

                # 转换视频数据
                video_item = TrendInsightVideoMapper.keyword_video_to_douyin_aweme_data(video, source_keyword)
                video_items.append(video_item)
                video_ids.append(item_id)

            except (AttributeError, Exception):
                # 记录错误但继续处理其他视频
                # 这里可以添加日志记录
                continue

        return video_items, video_ids



    @staticmethod
    def douyin_rpc_aweme_list_to_douyin_aweme_data_list(
        aweme_list: list, source_keyword: str = "author_sync"
    ) -> tuple[list["DouyinAwemeData"], list[str]]:
        """
        将抖音 RPC API 返回的 aweme_list 转换为 DouyinAwemeData 列表

        Args:
            aweme_list: 抖音 RPC API 返回的视频列表 (List[Dict[str, Any]])
            source_keyword: 数据来源标识，默认为 "author_sync"

        Returns:
            tuple: (DouyinAwemeData 列表, 视频ID列表)
        """
        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        video_data_list: list[DouyinAwemeData] = []
        video_ids: list[str] = []

        for video_info in aweme_list:
            try:
                # 确保是字典类型
                if not isinstance(video_info, dict):
                    continue

                # 提取基本信息
                aweme_id = video_info.get("aweme_id", "")
                if not aweme_id:
                    continue

                # 提取作者信息
                author = video_info.get("author", {})
                if not isinstance(author, dict):
                    author = {}

                # 提取统计信息
                statistics = video_info.get("statistics", {})
                if not isinstance(statistics, dict):
                    statistics = {}

                # 创建 DouyinAwemeData 实例
                video_data = DouyinAwemeData(
                    # 基本视频信息
                    aweme_id=aweme_id,
                    title=video_info.get("desc", ""),
                    desc=video_info.get("desc", ""),
                    aweme_type=str(video_info.get("aweme_type", "0")),
                    create_time=video_info.get("create_time", 0),

                    # 用户信息
                    user_id=author.get("uid", ""),
                    sec_uid=author.get("sec_uid", ""),
                    short_user_id=author.get("short_id", ""),
                    user_unique_id=author.get("unique_id", ""),
                    nickname=author.get("nickname", ""),
                    avatar=TrendInsightVideoMapper._extract_avatar_url(author),
                    user_signature=author.get("signature", ""),

                    # 统计信息
                    liked_count=str(statistics.get("digg_count", 0)),
                    comment_count=str(statistics.get("comment_count", 0)),
                    share_count=str(statistics.get("share_count", 0)),
                    collected_count=str(statistics.get("collect_count", 0)),

                    # 搜索来源
                    source_keyword=source_keyword,
                )

                video_data_list.append(video_data)
                video_ids.append(aweme_id)

            except Exception:
                # 记录错误但继续处理其他视频
                continue

        return video_data_list, video_ids

    @staticmethod
    def top_video_info_to_douyin_aweme_data(video: "TopVideoInfo", source_keyword: str) -> "DouyinAwemeData":
        """
        将 TrendInsight get_great_user_top_video 返回的 TopVideoInfo 转换为 DouyinAwemeData

        Args:
            video: TrendInsight API get_great_user_top_video 返回的视频对象
            source_keyword: 搜索来源关键字

        Returns:
            DouyinAwemeData: 转换后的视频数据模型

        Raises:
            AttributeError: 当视频对象缺少必要的 item_id 字段时抛出
        """
        # 使用强类型访问而不是 getattr
        item_id = video.item_id
        if not item_id:
            raise AttributeError("视频对象缺少必要的 item_id 字段")

        # 解析发布时间 - TopVideoInfo 中的 create_time 是 YYYYMMDD 格式
        create_time_str: Optional[str] = video.create_time
        publish_time: datetime = TrendInsightVideoMapper._parse_date_to_datetime(create_time_str)

        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        return DouyinAwemeData(
            # 用户信息 - TopVideoInfo 中没有详细的用户信息
            user_id=None,
            sec_uid=None,
            short_user_id=None,
            user_unique_id=None,
            nickname=None,  # TopVideoInfo 中没有用户昵称
            avatar=None,
            user_signature=None,
            ip_location=None,
            # 视频信息
            aweme_id=item_id,
            aweme_type=ContentType.VIDEO.value,  # 默认视频类型
            title=video.video_text[:1000] if video.video_text else None,
            desc=video.video_text,
            create_time=publish_time,
            # 统计信息 - TopVideoInfo 中有完整的统计数据
            liked_count=video.like_cnt or "0",
            comment_count=video.coment_cnt or "0",  # 注意：API 中是 coment_cnt 不是 comment_cnt
            share_count=video.share_cnt or "0",
            collected_count="0",  # TopVideoInfo 中没有收藏数，使用关注数作为替代或设为0
            # 媒体信息
            aweme_url=TrendInsightVideoMapper._extract_aweme_detail_url(item_id),
            cover_url=video.picture,
            video_download_url=None,  # TopVideoInfo 中没有视频下载地址
            # 搜索来源
            source_keyword=source_keyword,
        )

    @staticmethod
    def top_video_list_to_douyin_aweme_data_list(
        video_list: List["TopVideoInfo"], source_keyword: str
    ) -> tuple[list["DouyinAwemeData"], list[str]]:
        """
        批量将 TrendInsight get_great_user_top_video 返回的 TopVideoInfo 列表转换为 DouyinAwemeData 列表

        Args:
            video_list: TrendInsight API get_great_user_top_video 返回的视频对象列表 (List[TopVideoInfo])
            source_keyword: 搜索来源关键字

        Returns:
            tuple: (转换成功的 DouyinAwemeData 列表, 视频ID列表)
        """
        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData

        video_items: list[DouyinAwemeData] = []
        video_ids: list[str] = []

        for video in video_list:
            try:
                # 使用强类型访问而不是 getattr
                item_id = video.item_id
                if not item_id:
                    continue

                # 转换视频数据
                video_item = TrendInsightVideoMapper.top_video_info_to_douyin_aweme_data(video, source_keyword)
                video_items.append(video_item)
                video_ids.append(item_id)

            except (AttributeError, Exception):
                # 记录错误但继续处理其他视频
                # 这里可以添加日志记录
                continue

        return video_items, video_ids

    @staticmethod
    def _parse_date_string(date_str: Optional[str]) -> int:
        """
        解析日期字符串（YYYYMMDD 格式）为时间戳

        Args:
            date_str: 日期字符串，格式为 "YYYYMMDD"

        Returns:
            int: Unix 时间戳，解析失败时返回 0
        """
        if not date_str:
            return 0

        try:
            # 解析 YYYYMMDD 格式的日期字符串
            dt = datetime.strptime(date_str, "%Y%m%d")
            return int(dt.timestamp())
        except (ValueError, TypeError):
            return 0

    @staticmethod
    def _parse_date_to_datetime(date_str: Optional[str]) -> datetime:
        """
        解析日期字符串（YYYYMMDD 格式）为 datetime 对象

        Args:
            date_str: 日期字符串，格式为 "YYYYMMDD"

        Returns:
            datetime: datetime 对象，解析失败时返回当前时间
        """
        if not date_str:
            return datetime.now()

        try:
            # 解析 YYYYMMDD 格式的日期字符串
            return datetime.strptime(date_str, "%Y%m%d")
        except (ValueError, TypeError):
            return datetime.now()

    @staticmethod
    def _extract_avatar_url(author: dict) -> str:
        """
        从作者信息中提取头像URL

        Args:
            author: 作者信息字典

        Returns:
            str: 头像URL，如果没有则返回空字符串
        """
        try:
            # 尝试从 avatar_thumb 中获取
            avatar_thumb = author.get("avatar_thumb", {})
            if isinstance(avatar_thumb, dict):
                url_list = avatar_thumb.get("url_list", [])
                if isinstance(url_list, list) and len(url_list) > 0:
                    return url_list[0]

            # 尝试从 avatar_larger 中获取
            avatar_larger = author.get("avatar_larger", {})
            if isinstance(avatar_larger, dict):
                url_list = avatar_larger.get("url_list", [])
                if isinstance(url_list, list) and len(url_list) > 0:
                    return url_list[0]

            return ""
        except Exception:
            return ""

    @staticmethod
    def douyin_aweme_data_to_model(video_data: "DouyinAwemeData") -> "DouyinAweme":
        """
        将 DouyinAwemeData 转换为 DouyinAweme 数据库模型

        Args:
            video_data: DouyinAwemeData 实例

        Returns:
            DouyinAweme: 转换后的数据库模型实例
        """
        # 延迟导入避免循环依赖
        from models.douyin.models import DouyinAweme

        return DouyinAweme(
            aweme_id=video_data.aweme_id,
            aweme_type=video_data.aweme_type,
            title=video_data.title,
            desc=video_data.desc,
            create_time=video_data.create_time,
            user_id=video_data.user_id,
            sec_uid=video_data.sec_uid,
            short_user_id=video_data.short_user_id,
            user_unique_id=video_data.user_unique_id,
            nickname=video_data.nickname,
            avatar=video_data.avatar,
            user_signature=video_data.user_signature,
            ip_location=video_data.ip_location,
            liked_count=video_data.liked_count,
            comment_count=video_data.comment_count,
            share_count=video_data.share_count,
            collected_count=video_data.collected_count,
            aweme_url=video_data.aweme_url,
            cover_url=video_data.cover_url,
            video_download_url=video_data.video_download_url,
            source_keyword=video_data.source_keyword,
        )

    @staticmethod
    async def ensure_douyin_aweme_records(videos: list, source_keyword: str) -> tuple[int, int]:
        """
        向后兼容方法：委托给服务层处理视频记录的创建和更新
        
        Args:
            videos: 视频对象列表
            source_keyword: 搜索来源关键字
            
        Returns:
            tuple: (已创建记录数量, 已存在/更新记录数量)
        """
        # 延迟导入避免循环依赖
        from schemas.trendinsight import DouyinAwemeData
        from controllers.trendinsight.services import DouyinAwemeService
        
        # 转换视频数据
        video_items, video_ids = TrendInsightVideoMapper.videos_to_douyin_aweme_data_list(videos, source_keyword)
        
        # 委托给服务层处理数据库操作
        return await DouyinAwemeService.ensure_douyin_aweme_records(video_items, video_ids)
