"""
RPC data mapper for converting RPC API response data to standard format.
"""

from typing import Any, Dict

from .base import BaseDataMapper
from .exceptions import MappingException, ValidationException
from .pydantic_models import (
    DouyinVideoAuthor,
    DouyinVideoData,
    DouyinVideoDataItem,
    DouyinVideoMedia,
    DouyinVideoStatistics,
)


class RPCDataMapper(BaseDataMapper):
    """
    RPC数据映射器，处理从RPC API获取的数据
    """

    def map_to_standard_format(self, raw_data: Dict[str, Any]) -> DouyinVideoDataItem:
        """
        将RPC原始数据映射为标准格式

        Args:
            raw_data: RPC原始数据字典

        Returns:
            DouyinVideoDataItem: 标准格式的数据模型

        Raises:
            MappingException: 映射过程中发生错误
        """
        try:
            if not self.validate_raw_data(raw_data):
                raise ValidationException(
                    "RPC data validation failed",
                    validation_type="rpc_data",
                    failed_fields=["aweme_id"],
                    context={"mapper_type": "rpc"},
                )

            # 提取各部分数据
            basic_info = self.extract_basic_info(raw_data)
            user_info = self.extract_user_info(raw_data)
            statistics = self.extract_statistics(raw_data)
            media_urls = self.extract_media_urls(raw_data)
            rpc_specific = self._extract_rpc_specific_data(raw_data)

            # 组装 Pydantic 模型
            # DouyinVideoDataItem 使用平铺结构，不包含嵌套的 author、statistics、media 字段
            standard_data = DouyinVideoDataItem(
                **basic_info,
                **user_info,  # 平铺用户信息字段
                **{f"liked_count": str(statistics.get("like_count", 0))},  # 转换统计信息为字符串格式
                **{f"comment_count": str(statistics.get("comment_count", 0))},
                **{f"share_count": str(statistics.get("share_count", 0))},
                **{f"collected_count": str(statistics.get("collect_count", 0))},
                **{k: v for k, v in media_urls.items() if k in ["aweme_url", "cover_url", "video_download_url"]},  # 只包含媒体URL字段
                source_keyword="rpc",
                aweme_type=rpc_specific.get("aweme_type"),
                group_id_str=rpc_specific.get("group_id_str"),
            )

            return standard_data

        except Exception as e:
            if isinstance(e, (ValidationException, MappingException)):
                raise

            raise MappingException(
                f"Failed to map RPC data: {str(e)}",
                mapper_type="rpc",
                failed_field="unknown",
                raw_data_sample=str(raw_data)[:200],
                context={"original_error": str(e)},
            )

    def validate_raw_data(self, raw_data: Dict[str, Any]) -> bool:
        """
        验证RPC原始数据的有效性

        Args:
            raw_data: 原始数据字典

        Returns:
            bool: 数据是否有效
        """
        if not isinstance(raw_data, dict):
            return False

        # 检查必需字段 - RPC数据可能在不同的层级
        # 可能是直接的aweme_detail，也可能包装在aweme_detail字段中
        aweme_id = (
            self._safe_extract(raw_data, "aweme_id")
            or self._safe_extract(raw_data, ["aweme_detail", "aweme_id"])
            or self._safe_extract(raw_data, ["data", "aweme_id"])
        )

        return bool(aweme_id)

    def _extract_rpc_specific_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取RPC特有的数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: RPC特有数据字典
        """
        # 处理RPC特有的视频URL提取
        video_urls = self._process_rpc_video_urls(raw_data)

        # 提取RPC特有字段
        rpc_data = {
            "video_download_url": video_urls.get("download_url", ""),
        }

        # 如果video_download_url为空，使用play_addr作为备选
        if not rpc_data["video_download_url"]:
            rpc_data["video_download_url"] = video_urls.get("play_url", "")

        # 提取RPC响应中的额外信息
        rpc_data.update(self._extract_rpc_metadata(raw_data))

        return rpc_data

    def _process_rpc_video_urls(self, raw_data: Dict[str, Any]) -> Dict[str, str]:
        """
        处理RPC视频URL

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, str]: 包含各种URL的字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        video_info = self._safe_extract(aweme_detail, "video", {})

        if not isinstance(video_info, dict):
            return {}

        urls = {}

        # RPC响应通常包含完整的视频URL信息
        # 按优先级顺序尝试提取

        # 1. 优先使用H.264格式（最高质量）
        play_addr_h264 = video_info.get("play_addr_h264", {})
        if isinstance(play_addr_h264, dict):
            h264_url_list = play_addr_h264.get("url_list", [])
            if h264_url_list and len(h264_url_list) >= 2:  # RPC通常要求至少2个URL
                urls["download_url"] = h264_url_list[-1]

        # 2. 尝试使用download_addr
        if not urls.get("download_url"):
            download_addr = video_info.get("download_addr", {})
            if isinstance(download_addr, dict):
                download_url_list = download_addr.get("url_list", [])
                if download_url_list:
                    urls["download_url"] = download_url_list[-1]

        # 3. 使用通用play_addr作为备选
        play_addr = video_info.get("play_addr", {})
        if isinstance(play_addr, dict):
            play_url_list = play_addr.get("url_list", [])
            if play_url_list and len(play_url_list) >= 2:  # RPC通常要求至少2个URL
                if not urls.get("download_url"):
                    urls["download_url"] = play_url_list[-1]
                urls["play_url"] = play_url_list[-1]

        # 4. 尝试256p格式作为最后备选
        if not urls.get("download_url") and not urls.get("play_url"):
            play_addr_256 = video_info.get("play_addr_256", {})
            if isinstance(play_addr_256, dict):
                url_256_list = play_addr_256.get("url_list", [])
                if url_256_list and len(url_256_list) >= 2:
                    urls["play_url"] = url_256_list[-1]

        return urls

    def _extract_rpc_metadata(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取RPC特有的元数据

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: RPC元数据字典
        """
        metadata = {}

        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)

        # 提取aweme_type
        aweme_type = self._safe_extract(aweme_detail, "aweme_type", 0)
        if aweme_type:
            metadata["aweme_type"] = str(aweme_type)

        # 提取group_id_str（RPC特有字段）
        group_id_str = self._safe_extract(aweme_detail, "group_id_str", "")
        if group_id_str:
            metadata["group_id_str"] = group_id_str

        # 提取视频时长信息
        video_info = self._safe_extract(aweme_detail, "video", {})
        if isinstance(video_info, dict):
            duration = video_info.get("duration", 0)
            if duration:
                metadata["duration"] = duration

            # 提取视频尺寸信息
            width = video_info.get("width", 0)
            height = video_info.get("height", 0)
            if width and height:
                metadata["video_width"] = width
                metadata["video_height"] = height

        # 提取音乐信息
        music_info = self._safe_extract(aweme_detail, "music", {})
        if isinstance(music_info, dict):
            music_title = music_info.get("title", "")
            if music_title:
                metadata["music_title"] = music_title

            music_id = music_info.get("id", "")
            if music_id:
                metadata["music_id"] = str(music_id)

        # 提取risk_infos（RPC特有的风险信息）
        risk_infos = self._safe_extract(aweme_detail, "risk_infos", {})
        if isinstance(risk_infos, dict) and risk_infos:
            metadata["risk_infos"] = str(risk_infos)

        return metadata

    def _extract_video_download_url(self, aweme_detail: Dict[str, Any], by_mobile: bool = False) -> str:
        """
        重写父类方法，专门处理RPC视频下载URL提取

        Args:
            aweme_detail: 抖音视频详情数据
            by_mobile: 是否为移动端数据（RPC默认False）

        Returns:
            str: 视频下载地址
        """
        if not aweme_detail:
            return ""

        video_urls = self._process_rpc_video_urls(
            {"aweme_detail": aweme_detail} if "video" in aweme_detail else aweme_detail
        )

        # 优先使用下载地址
        download_url = video_urls.get("download_url", "")
        if download_url:
            return download_url

        # 使用播放地址作为备选
        play_url = video_urls.get("play_url", "")
        if play_url:
            return play_url

        # 如果RPC特有逻辑都没有找到，回退到父类方法
        return super()._extract_video_download_url(aweme_detail, by_mobile=False)

    def extract_basic_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的基础信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 基础信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)

        return {
            "aweme_id": self._safe_extract(aweme_detail, "aweme_id", ""),
            "title": self._safe_extract(aweme_detail, "desc", ""),
            "desc": self._safe_extract(aweme_detail, "desc", ""),
            "aweme_type": str(self._safe_extract(aweme_detail, "aweme_type", "0")),
            "create_time": self._convert_to_datetime(self._safe_extract(aweme_detail, "create_time")),
        }

    def extract_user_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的用户信息格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 用户信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        author = self._safe_extract(aweme_detail, "author", {})

        if not isinstance(author, dict):
            author = {}

        return {
            "user_id": self._safe_extract(author, ["uid", "user_id"], ""),
            "sec_uid": self._safe_extract(author, "sec_uid", ""),
            "short_user_id": self._safe_extract(author, ["short_id", "short_user_id"], ""),
            "user_unique_id": self._safe_extract(author, "unique_id", ""),
            "nickname": self._safe_extract(author, "nickname", ""),
            "avatar": self._extract_avatar_url(author),
            "user_signature": self._safe_extract(author, ["signature", "desc"], ""),
            "ip_location": self._safe_extract(aweme_detail, "ip_location", ""),
        }

    def extract_statistics(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        重写父类方法，处理RPC特有的统计数据格式

        Args:
            raw_data: 原始数据字典

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        # RPC数据可能在aweme_detail字段中
        aweme_detail = self._safe_extract(raw_data, "aweme_detail", raw_data)
        statistics = self._safe_extract(aweme_detail, "statistics", {})

        if not isinstance(statistics, dict):
            statistics = {}

        # RPC的统计数据格式通常很标准
        return {
            "liked_count": str(self._safe_extract(statistics, "digg_count", 0)),
            "comment_count": str(self._safe_extract(statistics, "comment_count", 0)),
            "share_count": str(self._safe_extract(statistics, "share_count", 0)),
            "collected_count": str(self._safe_extract(statistics, ["collect_count", "collected_count"], 0)),
        }
