"""
Tests for CollectionDataMapper
"""

import json
import os
from typing import Dict, List

import pytest
from rpc.douyin.schemas import CollectVideoListResponse

from mappers.douyin.collection_mapper import CollectionDataMapper
from models.douyin.models import DouyinAweme


def load_test_data() -> Dict:
    """Load test data from mock file"""
    mock_file_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
        "rpc",
        "douyin",
        "tests",
        "mock",
        "get_collect_video_list.json"
    )
    
    with open(mock_file_path, "r", encoding="utf-8") as f:
        return json.load(f)


class TestCollectionDataMapper:
    """Test cases for CollectionDataMapper"""

    def test_map_video_info_to_douyin_aweme(self):
        """Test mapping single VideoInfo to DouyinAweme"""
        # Load test data
        test_data = load_test_data()
        response = CollectVideoListResponse(**test_data)
        
        # Get the first video from aweme_list
        video = response.aweme_list[0]
        
        # Map to DouyinAweme
        aweme = CollectionDataMapper.map_video_info_to_douyin_aweme(video)
        
        # Assertions
        assert isinstance(aweme, DouyinAweme)
        assert aweme.aweme_id == video.aweme_id
        assert aweme.desc == video.desc
        assert aweme.create_time == video.create_time
        assert aweme.aweme_type == str(video.aweme_type)
        
        # Test author-related fields
        if video.author:
            assert aweme.sec_uid == video.author.sec_uid
            assert aweme.user_unique_id == getattr(video.author, "unique_id", None)
            assert aweme.nickname == video.author.nickname
            assert aweme.user_signature == getattr(video.author, "signature", None)
        
        # Test user_id
        if video.author_user_id:
            assert aweme.user_id == str(video.author_user_id)
        
        # Test statistics-related fields
        if video.statistics:
            assert aweme.liked_count == str(video.statistics.digg_count)
            assert aweme.comment_count == str(video.statistics.comment_count)
            assert aweme.share_count == str(video.statistics.share_count)
            assert aweme.collected_count == str(video.statistics.collect_count)
        
        # Test source_keyword has default value
        assert aweme.source_keyword == ""

    def test_map_video_infos_to_douyin_awemes(self):
        """Test mapping list of VideoInfo to list of DouyinAweme"""
        # Load test data
        test_data = load_test_data()
        response = CollectVideoListResponse(**test_data)
        
        # Map all videos
        awemes = CollectionDataMapper.map_video_infos_to_douyin_awemes(response.aweme_list)
        
        # Assertions
        assert isinstance(awemes, list)
        assert len(awemes) == len(response.aweme_list)
        
        # Check that all items are DouyinAweme instances
        for aweme in awemes:
            assert isinstance(aweme, DouyinAweme)
        
        # Check that aweme_ids match
        for i, (aweme, video) in enumerate(zip(awemes, response.aweme_list)):
            assert aweme.aweme_id == video.aweme_id, f"Mismatch at index {i}"

    def test_map_empty_list(self):
        """Test mapping empty list"""
        # Test with empty list
        awemes = CollectionDataMapper.map_video_infos_to_douyin_awemes([])
        
        # Assertions
        assert isinstance(awemes, list)
        assert len(awemes) == 0

    def test_map_video_with_missing_fields(self):
        """Test mapping VideoInfo with missing optional fields"""
        # Load test data
        test_data = load_test_data()
        response = CollectVideoListResponse(**test_data)
        
        # Get a video and remove some optional fields
        video = response.aweme_list[0]
        video.author = None
        video.music = None
        video.statistics = None
        video.duration = None
        
        # Map to DouyinAweme
        aweme = CollectionDataMapper.map_video_info_to_douyin_aweme(video)
        
        # Assertions for missing fields
        assert isinstance(aweme, DouyinAweme)
        assert aweme.sec_uid is None
        assert aweme.user_unique_id is None
        assert aweme.nickname is None
        assert aweme.liked_count == "0"
        assert aweme.comment_count == "0"
        assert aweme.collected_count == "0"
        assert aweme.share_count == "0"