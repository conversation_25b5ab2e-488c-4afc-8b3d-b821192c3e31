#!/usr/bin/env python3
"""
测试关键词同步功能的后台任务处理

这个脚本用于测试修改后的 sync_keyword_videos 方法，验证：
1. 关键词视频同步功能正常工作
2. 后台任务能够正确处理 index 字段
3. index 值能够正确存储到数据库中
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from controllers.trendinsight.keyword_sync_controller import keyword_sync_controller
from controllers.trendinsight.video_process_controller import video_process_controller
from models.trendinsight.models import TrendInsightVideo


async def test_keyword_sync_with_background_tasks():
    """测试关键词同步功能和后台任务处理"""
    
    print("🚀 开始测试关键词同步功能的后台任务处理")
    
    # 测试关键词
    test_keyword = "科技前沿"
    
    try:
        # 1. 执行关键词同步
        print(f"\n📋 步骤1: 同步关键词 '{test_keyword}' 的视频数据")
        sync_result = await keyword_sync_controller.sync_keyword_videos(test_keyword)
        
        print(f"✅ 关键词同步完成:")
        print(f"   - 关键词操作: {sync_result.keyword_action}")
        print(f"   - 同步视频数: {sync_result.videos_synced}")
        print(f"   - 新建关联: {sync_result.relations_created}")
        print(f"   - 已存在关联: {sync_result.relations_existing}")
        print(f"   - 视频项目数: {len(sync_result.video_items)}")
        
        # 2. 提取视频指数数据
        print(f"\n📊 步骤2: 提取视频指数数据")
        video_index_data = []
        for video_item in sync_result.video_items:
            if video_item.index:
                video_index_data.append({
                    'aweme_id': video_item.aweme_id,
                    'index': video_item.index
                })
                print(f"   - 视频 {video_item.aweme_id}: index = {video_item.index}")
        
        print(f"✅ 提取到 {len(video_index_data)} 个视频的指数数据")
        
        # 3. 模拟后台任务处理
        if video_index_data:
            print(f"\n⚙️ 步骤3: 执行后台任务处理指数数据")
            await video_process_controller.process_keyword_video_index_data(video_index_data)
            print("✅ 后台任务处理完成")
            
            # 4. 验证数据库中的数据
            print(f"\n🔍 步骤4: 验证数据库中的指数数据")
            for video_data in video_index_data[:3]:  # 只检查前3个视频
                aweme_id = video_data['aweme_id']
                try:
                    video_record = await TrendInsightVideo.get(id=aweme_id)
                    print(f"   - 视频 {aweme_id}:")
                    print(f"     * keyword_index: {video_record.keyword_index}")
                    print(f"     * trend_score: {video_record.trend_score}")
                    print(f"     * trend_radio: {video_record.trend_radio}")
                except Exception as e:
                    print(f"   - 视频 {aweme_id}: 查询失败 - {str(e)}")
            
            print("✅ 数据库验证完成")
        else:
            print("⚠️ 没有找到包含 index 值的视频数据")
        
        print(f"\n🎉 测试完成！关键词同步和后台任务处理功能正常工作")
        
        # 5. 输出测试总结
        print(f"\n📈 测试总结:")
        print(f"   - 测试关键词: {test_keyword}")
        print(f"   - 同步视频总数: {sync_result.videos_synced}")
        print(f"   - 处理指数数据: {len(video_index_data)} 个")
        print(f"   - 错误数量: {len(sync_result.errors)}")
        
        if sync_result.errors:
            print(f"\n❌ 发现错误:")
            for error in sync_result.errors:
                print(f"   - {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("关键词同步后台任务处理测试")
    print("=" * 60)
    
    # 初始化数据库连接
    try:
        from tortoise import Tortoise
        from config.database import DATABASE_CONFIG
        
        await Tortoise.init(config=DATABASE_CONFIG)
        print("✅ 数据库连接初始化成功")
        
        # 执行测试
        success = await test_keyword_sync_with_background_tasks()
        
        if success:
            print("\n🎊 所有测试通过！")
        else:
            print("\n💥 测试失败！")
            
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("✅ 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
