#!/usr/bin/env python3
"""
本地测试脚本 - 用于开发和调试任务系统
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import logging

from tasks.main import execute_task, handler

# 配置详细日志
logging.basicConfig(level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def test_command_line():
    """测试命令行模式"""
    print("=== 测试命令行模式 ===")

    # 测试配置
    test_configs = [
        # 基本测试
        {"task_type": "trend_refresh", "batch_size": 5},
        # 带过滤器的测试
        {"task_type": "trend_refresh", "batch_size": 3, "filters": {"max_results": 3}},  # 限制结果数量，便于测试
    ]

    for i, config in enumerate(test_configs, 1):
        print(f"\n--- 测试配置 {i} ---")
        print(f"配置: {json.dumps(config, ensure_ascii=False, indent=2)}")

        try:
            result = asyncio.run(execute_task(config))
            print(f"执行结果: {result.to_json()}")
            print(f"状态: {result.status}")
            print(f"处理数量: {result.processed_count}")
            print(f"成功数量: {result.success_count}")
            print(f"失败数量: {result.failed_count}")

            if result.errors:
                print(f"错误信息: {result.errors}")

        except Exception as e:
            print(f"执行异常: {str(e)}")
            logger.exception("详细错误信息")


def test_function_compute():
    """测试Function Compute模式"""
    print("\n=== 测试Function Compute模式 ===")

    # 模拟Function Compute事件
    test_events = [
        # 标准时间触发器事件
        {
            "triggerTime": "2025-01-21T10:00:00Z",
            "triggerName": "trend-refresh-hourly",
            "payload": json.dumps({"task_type": "trend_refresh", "batch_size": 5}),
        },
        # 带配置的事件
        {
            "triggerTime": "2025-01-21T10:00:00Z",
            "triggerName": "trend-refresh-test",
            "payload": json.dumps({"task_type": "trend_refresh", "batch_size": 3, "filters": {"max_results": 3}}),
        },
    ]

    # 模拟Context对象
    class MockContext:
        def __init__(self):
            self.request_id = "test-request-123"
            self.function_name = "qihaozhushou-tasks-scheduler"

    context = MockContext()

    for i, event in enumerate(test_events, 1):
        print(f"\n--- Function Compute 测试 {i} ---")
        print(f"事件: {json.dumps(event, ensure_ascii=False, indent=2)}")

        try:
            response = handler(event, context)
            print(f"响应状态码: {response['statusCode']}")

            if response["statusCode"] == 200:
                body = response["body"]
                print(f"任务状态: {body.get('status', 'unknown')}")
                print(f"处理数量: {body.get('processed_count', 0)}")
                print(f"成功数量: {body.get('success_count', 0)}")
                print(f"失败数量: {body.get('failed_count', 0)}")
            else:
                print(f"错误响应: {response['body']}")

        except Exception as e:
            print(f"执行异常: {str(e)}")
            logger.exception("详细错误信息")


def test_config_parsing():
    """测试配置解析"""
    print("\n=== 测试配置解析 ===")

    from tasks.main import get_task_config_from_event

    test_cases = [
        # 字符串事件
        json.dumps(
            {
                "triggerTime": "2025-01-21T10:00:00Z",
                "payload": json.dumps({"task_type": "trend_refresh", "batch_size": 50}),
            }
        ),
        # 字典事件
        {"triggerTime": "2025-01-21T10:00:00Z", "payload": {"task_type": "trend_refresh", "batch_size": 100}},
        # 带字符串payload的字典事件
        {
            "triggerTime": "2025-01-21T10:00:00Z",
            "payload": json.dumps({"task_type": "trend_refresh", "batch_size": 200}),
        },
    ]

    class MockContext:
        request_id = "test-123"

    for i, event in enumerate(test_cases, 1):
        print(f"\n--- 配置解析测试 {i} ---")
        print(f"输入事件: {event}")

        try:
            config = get_task_config_from_event(event, MockContext())
            print(f"解析结果: {json.dumps(config, ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"解析异常: {str(e)}")
            logger.exception("详细错误信息")


def main():
    """主函数"""
    print("起号助手任务系统本地测试")
    print("=" * 50)

    # 检查参数
    if len(sys.argv) > 1:
        test_mode = sys.argv[1]
    else:
        test_mode = "all"

    if test_mode in ["all", "cli", "command"]:
        test_command_line()

    if test_mode in ["all", "fc", "function"]:
        test_function_compute()

    if test_mode in ["all", "config", "parse"]:
        test_config_parsing()

    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试出现异常: {str(e)}")
        logger.exception("详细异常信息")
