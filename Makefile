# Build configuration
# -------------------

APP_NAME := `sed -n 's/^ *name.*=.*"\([^"]*\)".*/\1/p' pyproject.toml`
APP_VERSION := `sed -n 's/^ *version.*=.*"\([^"]*\)".*/\1/p' pyproject.toml`
GIT_REVISION = `git rev-parse HEAD`

# Introspection targets
# ---------------------

.PHONY: help
help: header targets

.PHONY: header
header:
	@echo "\033[34mEnvironment\033[0m"
	@echo "\033[34m---------------------------------------------------------------\033[0m"
	@printf "\033[33m%-23s\033[0m" "APP_NAME"
	@printf "\033[35m%s\033[0m" $(APP_NAME)
	@echo ""
	@printf "\033[33m%-23s\033[0m" "APP_VERSION"
	@printf "\033[35m%s\033[0m" $(APP_VERSION)
	@echo ""
	@printf "\033[33m%-23s\033[0m" "GIT_REVISION"
	@printf "\033[35m%s\033[0m" $(GIT_REVISION)
	@echo "\n"

.PHONY: targets
targets:
	@echo "\033[34mDevelopment Targets\033[0m"
	@echo "\033[34m---------------------------------------------------------------\033[0m"
	@perl -nle'print $& if m{^[a-zA-Z_-]+:.*?## .*$$}' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-22s\033[0m %s\n", $$1, $$2}'

# Development targets
# -------------

.PHONY: install
install: ## Install dependencies
	uv add pyproject.toml


.PHONY: dev
dev: start

.PHONY: start
start: ## Starts the server
	python run.py

# Check, lint and format targets
# ------------------------------

.PHONY: check
check: check-format lint

.PHONY: check-format
check-format: ## Dry-run code formatter
	black ./ --check
	isort ./ --profile black --check

.PHONY: lint
lint: ## Run ruff
	ruff check ./
 
.PHONY: format
format: ## Run code formatter
	black ./
	isort ./ --profile black


.PHONY: test
test: ## Run the test suite
	$(eval include .env)
	$(eval export $(shell sed 's/=.*//' .env))
	pytest -vv -s --cache-clear ./

# Database management targets
# ---------------------------

.PHONY: db-quick
db-quick: ## 快速初始化数据库（开发环境推荐）
	python scripts/quick_init.py

.PHONY: db-init
db-init: ## 初始化数据库（安全模式，保留现有数据）
	python scripts/init_database.py

.PHONY: db-reset
db-reset: ## 重置数据库（危险操作，删除所有数据）
	python scripts/init_database.py --drop-tables

.PHONY: db-tables
db-tables: ## 只创建表结构，跳过数据初始化
	python scripts/init_database.py --skip-data

.PHONY: db-verify
db-verify: ## 验证数据库状态
	python scripts/init_database.py --verify-only

.PHONY: db-check
db-check: ## 检查数据库连接和状态
	python scripts/check_database.py

.PHONY: db-force-reset
db-force-reset: ## 强制重置数据库（不询问确认）
	python scripts/init_database.py --drop-tables --force

.PHONY: clean-db
clean-db: ## 删除migrations文件夹和db.sqlite3
	find . -type d -name "migrations" -exec rm -rf {} +
	rm -f db.sqlite3 db.sqlite3-shm db.sqlite3-wal

.PHONY: migrate
migrate: ## 运行aerich migrate命令生成迁移文件
	aerich migrate

.PHONY: upgrade
upgrade: ## 运行aerich upgrade命令应用迁移
	aerich upgrade
