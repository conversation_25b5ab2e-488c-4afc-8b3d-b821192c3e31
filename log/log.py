import sys
from pathlib import Path

from loguru import logger as loguru_logger

from settings import settings


class Loggin:
    def __init__(self) -> None:
        debug = settings.DEBUG
        if debug:
            self.level = "DEBUG"
        else:
            self.level = "INFO"

    def setup_logger(self):
        loguru_logger.remove()
        loguru_logger.add(sink=sys.stdout, level=self.level)

        # 配置文件日志输出到 logs 目录
        logs_dir = Path(__file__).parent.parent / "logs"
        logs_dir.mkdir(exist_ok=True)

        loguru_logger.add(
            str(logs_dir / "application.log"),
            level=self.level,
            rotation="100 MB",
            retention="30 days",
            compression="gz",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            enqueue=True,
        )

        # 配置 Tortoise ORM SQL 日志
        self._setup_tortoise_logging()

        return loguru_logger

    def _setup_tortoise_logging(self):
        """配置 Tortoise ORM 的 SQL 日志"""
        import logging

        # 设置 Tortoise 相关的日志级别
        tortoise_loggers = [
            "tortoise.db_client",  # 数据库客户端日志
            "tortoise",  # 通用 Tortoise 日志
            "tortoise.backends.mysql",  # MySQL 后端日志
        ]

        for logger_name in tortoise_loggers:
            logging.getLogger(logger_name).setLevel(logging.DEBUG if self.level == "DEBUG" else logging.INFO)

        # 将 Python 标准日志重定向到 loguru
        class InterceptHandler(logging.Handler):
            def emit(self, record):
                # 获取对应的 loguru 级别
                try:
                    level = loguru_logger.level(record.levelname).name
                except ValueError:
                    level = record.levelno

                # 查找调用者
                frame, depth = logging.currentframe(), 2
                while frame.f_code.co_filename == logging.__file__:
                    frame = frame.f_back
                    depth += 1

                loguru_logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

        # 设置拦截器
        logging.basicConfig(handlers=[InterceptHandler()], level=0)


loggin = Loggin()
logger = loggin.setup_logger()
