"""
任务系统数据模型

定义任务配置、结果等数据结构
"""

import json
from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, field_validator


class TaskConfig(BaseModel):
    """任务配置模型"""

    task_type: str
    batch_size: int = 100
    timeout: int = 3600  # 超时时间（秒）
    max_age_hours: int = 1  # 数据过期时间（小时）
    filters: Optional[Dict] = None

    # 作者监控任务特定配置
    author_video_limit: int = 50  # 每个作者最多获取的视频数量

    # 关键词监控任务特定配置
    keyword_video_limit: int = 100  # 每个关键词最多获取的视频数量
    keyword_search_days: int = 7  # 关键词搜索的天数范围

    @field_validator("task_type")
    @classmethod
    def validate_task_type(cls, v):
        allowed_types = ["trend_refresh", "author_monitor", "keyword_monitor"]
        if v not in allowed_types:
            raise ValueError(f"task_type must be one of {allowed_types}")
        return v

    @field_validator("batch_size")
    @classmethod
    def validate_batch_size(cls, v):
        if v <= 0 or v > 1000:
            raise ValueError("batch_size must be between 1 and 1000")
        return v

    @field_validator("timeout")
    @classmethod
    def validate_timeout(cls, v):
        if v <= 0 or v > 86400:  # 最多24小时
            raise ValueError("timeout must be between 1 and 86400 seconds")
        return v

    @field_validator("max_age_hours")
    @classmethod
    def validate_max_age_hours(cls, v):
        if v <= 0 or v > 168:  # 最多7天
            raise ValueError("max_age_hours must be between 1 and 168 hours")
        return v

    @field_validator("author_video_limit")
    @classmethod
    def validate_author_video_limit(cls, v):
        if v <= 0 or v > 200:
            raise ValueError("author_video_limit must be between 1 and 200")
        return v

    @field_validator("keyword_video_limit")
    @classmethod
    def validate_keyword_video_limit(cls, v):
        if v <= 0 or v > 500:
            raise ValueError("keyword_video_limit must be between 1 and 500")
        return v

    @field_validator("keyword_search_days")
    @classmethod
    def validate_keyword_search_days(cls, v):
        if v <= 0 or v > 30:
            raise ValueError("keyword_search_days must be between 1 and 30 days")
        return v


class TaskResult(BaseModel):
    """任务执行结果"""

    task_type: str
    status: str  # "success" | "failed" | "partial"
    start_time: datetime
    end_time: datetime
    duration: float  # 执行时长（秒）
    processed_count: int  # 处理的记录数
    success_count: int  # 成功数量
    failed_count: int  # 失败数量
    errors: List[str] = []  # 错误信息列表

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.processed_count == 0:
            return 0.0
        return (self.success_count / self.processed_count) * 100

    def to_json(self) -> str:
        """转换为 JSON 字符串"""
        return json.dumps(self.model_dump(), indent=2, default=str, ensure_ascii=False)
