"""
任务管理器

负责任务路由和执行控制
"""

import asyncio
from typing import Dict, Type

from .base import BaseTask
from .logger import TaskLogger
from .models import TaskConfig, TaskResult


class TaskManager:
    """任务管理器 - 负责任务路由和执行控制"""

    def __init__(self):
        """初始化任务管理器"""
        self.tasks: Dict[str, Type[BaseTask]] = {}
        self.logger = TaskLogger("task_manager")

    def register_task(self, task_type: str, task_class: Type[BaseTask]) -> None:
        """
        注册任务类型

        Args:
            task_type: 任务类型名称
            task_class: 任务类
        """
        self.tasks[task_type] = task_class
        self.logger.log_debug(f"已注册任务类型: {task_type}")

    def validate_config(self, config_dict: dict) -> TaskConfig:
        """
        验证任务配置

        Args:
            config_dict: 配置字典

        Returns:
            TaskConfig: 验证后的配置对象

        Raises:
            ValueError: 配置无效时抛出
        """
        try:
            config = TaskConfig(**config_dict)

            # 检查任务类型是否已注册
            if config.task_type not in self.tasks:
                available_types = list(self.tasks.keys())
                raise ValueError(f"未知的任务类型: {config.task_type}，可用类型: {available_types}")

            return config

        except Exception as e:
            self.logger.log_error(f"配置验证失败: {str(e)}")
            raise ValueError(f"配置验证失败: {str(e)}")

    async def execute_task(self, config: TaskConfig) -> TaskResult:
        """
        执行任务

        Args:
            config: 任务配置

        Returns:
            TaskResult: 执行结果
        """
        task_type = config.task_type

        if task_type not in self.tasks:
            error_msg = f"未注册的任务类型: {task_type}"
            self.logger.log_error(error_msg)
            raise ValueError(error_msg)

        # 创建任务实例
        task_class = self.tasks[task_type]
        task_logger = TaskLogger(task_type)
        task_instance = task_class(config, task_logger)

        # 验证参数
        if not await task_instance.validate_params():
            error_msg = f"任务参数验证失败: {task_type}"
            self.logger.log_error(error_msg)
            raise ValueError(error_msg)

        # 记录任务开始
        task_logger.log_task_start(config)

        try:
            # 执行任务（带超时）
            result = await asyncio.wait_for(task_instance.execute(), timeout=config.timeout)

            # 记录任务完成
            task_logger.log_task_complete(result)
            return result

        except asyncio.TimeoutError:
            error_msg = f"任务执行超时: {task_type} (超时时间: {config.timeout}秒)"
            self.logger.log_error(error_msg)
            raise TimeoutError(error_msg)
        except Exception as e:
            error_msg = f"任务执行异常: {task_type}, 错误: {str(e)}"
            self.logger.log_error(error_msg)
            raise
