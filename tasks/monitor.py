"""
任务系统性能监控模块

提供执行指标收集、内存监控等功能
"""

import json
import time
from datetime import datetime
from typing import Dict, Optional

import psutil

from .logger import TaskLogger


class TaskMonitor:
    """任务性能监控器"""

    def __init__(self, task_type: str):
        """
        初始化监控器

        Args:
            task_type: 任务类型
        """
        self.task_type = task_type
        self.logger = TaskLogger(f"{task_type}_monitor")
        self.start_time = time.time()
        self.peak_memory = 0
        self.initial_memory = self._get_memory_usage()

    def _get_memory_usage(self) -> float:
        """
        获取当前内存使用量（MB）

        Returns:
            float: 内存使用量
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / 1024 / 1024  # 转换为MB
        except Exception:
            return 0.0

    def _get_cpu_usage(self) -> float:
        """
        获取当前CPU使用率

        Returns:
            float: CPU使用率百分比
        """
        try:
            return psutil.cpu_percent(interval=0.1)
        except Exception:
            return 0.0

    def record_memory_peak(self) -> None:
        """记录内存使用峰值"""
        current_memory = self._get_memory_usage()
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory

    def log_performance_metrics(
        self, processed_count: int = 0, api_calls: int = 0, extra_metrics: Optional[Dict] = None
    ) -> None:
        """
        记录性能指标

        Args:
            processed_count: 已处理数量
            api_calls: API 调用次数
            extra_metrics: 额外指标
        """
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        current_memory = self._get_memory_usage()
        cpu_usage = self._get_cpu_usage()

        # 更新内存峰值
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory

        metrics = {
            "event": "performance_metrics",
            "task_type": self.task_type,
            "elapsed_time": round(elapsed_time, 2),
            "processed_count": processed_count,
            "api_calls": api_calls,
            "processing_rate": round(processed_count / elapsed_time, 2) if elapsed_time > 0 else 0,
            "api_rate": round(api_calls / elapsed_time, 2) if elapsed_time > 0 else 0,
            "memory": {
                "current_mb": round(current_memory, 2),
                "peak_mb": round(self.peak_memory, 2),
                "initial_mb": round(self.initial_memory, 2),
                "growth_mb": round(current_memory - self.initial_memory, 2),
            },
            "cpu_percent": round(cpu_usage, 2),
            "timestamp": datetime.now().isoformat(),
        }

        if extra_metrics:
            metrics.update(extra_metrics)

        self.logger.log_debug("性能指标记录", metrics)

    def log_api_timing(self, api_name: str, duration: float, success: bool = True) -> None:
        """
        记录API调用时间

        Args:
            api_name: API名称
            duration: 调用持续时间（秒）
            success: 是否成功
        """
        timing_info = {
            "event": "api_timing",
            "task_type": self.task_type,
            "api_name": api_name,
            "duration": round(duration, 3),
            "success": success,
            "timestamp": datetime.now().isoformat(),
        }

        if not success:
            self.logger.log_warning(f"API调用失败: {api_name}", timing_info)
        else:
            self.logger.log_debug(f"API调用完成: {api_name}", timing_info)

    def check_memory_threshold(self, threshold_mb: float = 500.0) -> bool:
        """
        检查内存使用是否超过阈值

        Args:
            threshold_mb: 内存阈值（MB）

        Returns:
            bool: 是否超过阈值
        """
        current_memory = self._get_memory_usage()

        if current_memory > threshold_mb:
            self.logger.log_warning(
                f"内存使用超过阈值: {current_memory:.2f}MB > {threshold_mb}MB",
                {"current_memory_mb": current_memory, "threshold_mb": threshold_mb, "peak_memory_mb": self.peak_memory},
            )
            return True

        return False

    def generate_performance_report(self, final_processed_count: int = 0, final_api_calls: int = 0) -> Dict:
        """
        生成性能报告

        Args:
            final_processed_count: 最终处理数量
            final_api_calls: 最终API调用次数

        Returns:
            Dict: 性能报告
        """
        end_time = time.time()
        total_duration = end_time - self.start_time
        final_memory = self._get_memory_usage()

        report = {
            "task_type": self.task_type,
            "execution": {
                "total_duration": round(total_duration, 2),
                "processed_count": final_processed_count,
                "api_calls": final_api_calls,
                "average_processing_rate": (
                    round(final_processed_count / total_duration, 2) if total_duration > 0 else 0
                ),
                "average_api_rate": round(final_api_calls / total_duration, 2) if total_duration > 0 else 0,
            },
            "memory": {
                "initial_mb": round(self.initial_memory, 2),
                "final_mb": round(final_memory, 2),
                "peak_mb": round(self.peak_memory, 2),
                "total_growth_mb": round(final_memory - self.initial_memory, 2),
                "max_growth_mb": round(self.peak_memory - self.initial_memory, 2),
            },
            "efficiency": {
                "memory_per_item": (
                    round(self.peak_memory / final_processed_count, 3) if final_processed_count > 0 else 0
                ),
                "time_per_item": round(total_duration / final_processed_count, 3) if final_processed_count > 0 else 0,
                "time_per_api_call": round(total_duration / final_api_calls, 3) if final_api_calls > 0 else 0,
            },
            "generated_at": datetime.now().isoformat(),
        }

        self.logger.log_debug("性能报告生成", report)
        return report


class PerformanceProfiler:
    """性能分析器上下文管理器"""

    def __init__(self, monitor: TaskMonitor, operation_name: str):
        """
        初始化分析器

        Args:
            monitor: 任务监控器
            operation_name: 操作名称
        """
        self.monitor = monitor
        self.operation_name = operation_name
        self.start_time = None

    def __enter__(self):
        """进入上下文"""
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.start_time:
            duration = time.time() - self.start_time
            success = exc_type is None
            self.monitor.log_api_timing(self.operation_name, duration, success)

        # 记录内存峰值
        self.monitor.record_memory_peak()


# 便利函数
def create_monitor(task_type: str) -> TaskMonitor:
    """
    创建任务监控器

    Args:
        task_type: 任务类型

    Returns:
        TaskMonitor: 监控器实例
    """
    return TaskMonitor(task_type)


def profile_operation(monitor: TaskMonitor, operation_name: str):
    """
    性能分析装饰器

    Args:
        monitor: 任务监控器
        operation_name: 操作名称

    Returns:
        PerformanceProfiler: 性能分析器
    """
    return PerformanceProfiler(monitor, operation_name)
