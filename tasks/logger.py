"""
任务系统日志记录器

提供结构化日志输出和进度记录功能
"""

import json
import logging
import sys
from datetime import datetime
from typing import Dict, Optional

from .models import TaskConfig, TaskResult


class TaskLogger:
    """任务日志记录器"""

    def __init__(self, task_type: str = "unknown"):
        """
        初始化日志记录器

        Args:
            task_type: 任务类型
        """
        self.task_type = task_type
        self.logger = logging.getLogger(f"tasks.{task_type}")

        # 配置日志格式
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def log_task_start(self, config: TaskConfig) -> None:
        """记录任务开始"""
        message = {
            "event": "task_start",
            "task_type": config.task_type,
            "config": config.model_dump(),
            "timestamp": datetime.now().isoformat(),
        }
        self.logger.info(json.dumps(message, ensure_ascii=False))

    def log_progress(self, processed: int, total: Optional[int] = None, message: str = "") -> None:
        """记录处理进度"""
        progress_info = {
            "event": "progress",
            "task_type": self.task_type,
            "processed": processed,
            "timestamp": datetime.now().isoformat(),
        }

        if total is not None:
            progress_info["total"] = total
            progress_info["percentage"] = round((processed / total) * 100, 2)

        if message:
            progress_info["message"] = message

        self.logger.info(json.dumps(progress_info, ensure_ascii=False))

    def log_task_complete(self, result: TaskResult) -> None:
        """记录任务完成"""
        message = {
            "event": "task_complete",
            "task_type": result.task_type,
            "status": result.status,
            "duration": result.duration,
            "processed_count": result.processed_count,
            "success_count": result.success_count,
            "failed_count": result.failed_count,
            "success_rate": result.success_rate,
            "timestamp": datetime.now().isoformat(),
        }

        if result.errors:
            message["error_count"] = len(result.errors)
            message["sample_errors"] = result.errors[:5]  # 只显示前5个错误

        level = logging.INFO if result.status == "success" else logging.ERROR
        self.logger.log(level, json.dumps(message, ensure_ascii=False))

    def log_error(self, error: str, extra_data: Optional[Dict] = None) -> None:
        """记录错误信息"""
        error_info = {
            "event": "error",
            "task_type": self.task_type,
            "error": error,
            "timestamp": datetime.now().isoformat(),
        }

        if extra_data:
            error_info.update(extra_data)

        self.logger.error(json.dumps(error_info, ensure_ascii=False))

    def log_warning(self, message: str, extra_data: Optional[Dict] = None) -> None:
        """记录警告信息"""
        warning_info = {
            "event": "warning",
            "task_type": self.task_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
        }

        if extra_data:
            warning_info.update(extra_data)

        self.logger.warning(json.dumps(warning_info, ensure_ascii=False))

    def log_debug(self, message: str, extra_data: Optional[Dict] = None) -> None:
        """记录调试信息"""
        debug_info = {
            "event": "debug",
            "task_type": self.task_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
        }

        if extra_data:
            debug_info.update(extra_data)

        self.logger.debug(json.dumps(debug_info, ensure_ascii=False))
