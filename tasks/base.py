"""
任务系统基类

定义任务执行的抽象接口
"""

import signal
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional

from .logger import TaskLogger
from .models import TaskConfig, TaskResult


class BaseTask(ABC):
    """任务基类 - 定义任务执行接口"""

    def __init__(self, config: TaskConfig, logger: TaskLogger):
        """
        初始化任务

        Args:
            config: 任务配置
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self._interrupted = False

        # 设置信号处理器，支持优雅中断
        signal.signal(signal.SIGTERM, self._handle_signal)
        signal.signal(signal.SIGINT, self._handle_signal)

    def _handle_signal(self, signum, frame):
        """处理中断信号"""
        self.logger.log_warning(f"收到中断信号 {signum}，准备优雅停止...")
        self._interrupted = True

    @property
    def is_interrupted(self) -> bool:
        """检查是否被中断"""
        return self._interrupted

    async def validate_params(self) -> bool:
        """
        验证任务参数

        Returns:
            bool: 参数是否有效
        """
        return True

    @abstractmethod
    async def execute(self) -> TaskResult:
        """
        执行任务 - 子类必须实现

        Returns:
            TaskResult: 执行结果
        """
        raise NotImplementedError

    def _create_result(
        self,
        status: str,
        start_time: datetime,
        processed_count: int = 0,
        success_count: int = 0,
        failed_count: int = 0,
        errors: Optional[list] = None,
    ) -> TaskResult:
        """
        创建任务结果对象

        Args:
            status: 执行状态
            start_time: 开始时间
            processed_count: 处理数量
            success_count: 成功数量
            failed_count: 失败数量
            errors: 错误列表

        Returns:
            TaskResult: 任务结果
        """
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        return TaskResult(
            task_type=self.config.task_type,
            status=status,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            processed_count=processed_count,
            success_count=success_count,
            failed_count=failed_count,
            errors=errors or [],
        )
