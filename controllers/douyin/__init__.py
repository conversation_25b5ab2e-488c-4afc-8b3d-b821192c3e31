"""
controllers/douyin 包

包含抖音平台相关的所有控制器业务逻辑
"""

from .controller import DouyinController
from .collection_controller import DouyinCollectionController, douyin_collection_controller
from .html_controller import DouyinHTMLController
from .main_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>roller as MainDouyinController
from .main_controller import douyin_controller
from .video_fetcher_controller import VideoFetcherController
from .video_url_parse_controller import VideoUrlParseController

# 为了向后兼容，保持原有的导入方式
# 同时从新的 controller.py 文件导入主要的控制器类
try:
    # 保持对 video_controller 的向后兼容
    from .video_controller import DouyinVideoController
except ImportError:
    # 如果 video_controller 被删除，使用新的 controller 中的 DouyinController
    DouyinVideoController = DouyinController

# 对外暴露的主要接口
__all__ = [
    "DouyinController",  # 新的主控制器
    "DouyinCollectionController",  # 收藏夹控制器
    "douyin_collection_controller",  # 收藏夹控制器实例
    "DouyinHTMLController",
    "DouyinVideoController",  # 向后兼容
    "MainDouyinController",  # main_controller 中的控制器
    "douyin_controller",  # 控制器实例
    "VideoFetcherController",  # 统一视频获取控制器
    "VideoUrlParseController",  # 视频URL解析控制器
]
