"""
controllers/douyin 使用示例

演示如何使用新的抖音控制器结构，包括：
- 精选页面数据获取
- 移动端分享页面数据获取  
- 智能自动选择方法
- 批量处理
"""

import asyncio

from loguru import logger

# 导入新的控制器
from controllers.douyin import DouyinHTMLController
from controllers.douyin.main_controller import douyin_controller


async def example_jingxuan_fetch():
    """示例：通过精选页面获取视频数据"""
    print("=" * 60)
    print("示例：通过精选页面获取视频数据")
    print("=" * 60)

    aweme_id = "7123456789012345678"  # 替换为真实的视频ID

    try:
        # 使用全局控制器实例
        result = await douyin_controller.fetch_jingxuan_data(aweme_id=aweme_id, use_proxy=True, save_to_db=True)

        if result["success"]:
            print("✅ 精选页面数据获取成功!")
            print(f"   - 视频ID: {result['data'].get('aweme_id', 'N/A')}")
            print(f"   - 标题: {result['data'].get('title', 'N/A')[:50]}...")
            print(f"   - 作者: {result['data'].get('nickname', 'N/A')}")
            print(f"   - 响应时间: {result['html_response']['response_time']:.2f}秒")
            print(f"   - 数据来源: {result['source']}")
        else:
            print(f"❌ 精选页面数据获取失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 异常: {e}")


async def example_mobile_fetch():
    """示例：通过移动端分享页面获取视频数据"""
    print("\n" + "=" * 60)
    print("示例：通过移动端分享页面获取视频数据")
    print("=" * 60)

    aweme_id = "7123456789012345678"  # 替换为真实的视频ID

    try:
        # 使用全局控制器实例
        result = await douyin_controller.fetch_mobile_data(
            aweme_id=aweme_id,
            use_proxy=True,
            custom_headers={
                "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15"
            },
            save_to_db=True,
        )

        if result["success"]:
            print("✅ 移动端数据获取成功!")
            print(f"   - 视频ID: {result['data'].get('aweme_id', 'N/A')}")
            print(f"   - 标题: {result['data'].get('title', 'N/A')[:50]}...")
            print(f"   - 作者: {result['data'].get('nickname', 'N/A')}")
            print(f"   - 响应时间: {result['html_response']['response_time']:.2f}秒")
            print(f"   - 数据来源: {result['source']}")
        else:
            print(f"❌ 移动端数据获取失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 异常: {e}")


async def example_auto_fetch():
    """示例：智能自动选择最佳方法获取数据"""
    print("\n" + "=" * 60)
    print("示例：智能自动选择最佳方法获取数据")
    print("=" * 60)

    aweme_id = "7123456789012345678"  # 替换为真实的视频ID

    try:
        # 使用智能获取，首选精选页面，失败则尝试移动端、RPC等
        result = await douyin_controller.fetch_video_data_auto(
            aweme_id=aweme_id,
            preferred_method="jingxuan",
            fallback_methods=["mobile", "rpc"],
            use_proxy=True,
            save_to_db=True,
        )

        if result["success"]:
            print("✅ 智能获取成功!")
            print(f"   - 使用方法: {result['method']}")
            print(f"   - 视频ID: {result['data'].get('aweme_id', 'N/A')}")
            print(f"   - 标题: {result['data'].get('title', 'N/A')[:50]}...")
            print(f"   - 作者: {result['data'].get('nickname', 'N/A')}")
            print(f"   - 数据来源: {result['source']}")
        else:
            print(f"❌ 智能获取失败: {result.get('error', '未知错误')}")
            print(f"   - 尝试的方法: {result.get('methods_tried', [])}")

    except Exception as e:
        print(f"❌ 异常: {e}")


async def example_batch_fetch():
    """示例：批量获取多个视频数据"""
    print("\n" + "=" * 60)
    print("示例：批量获取多个视频数据")
    print("=" * 60)

    # 测试视频ID列表（替换为真实的视频ID）
    aweme_ids = [
        "7123456789012345678",
        "7123456789012345679",
        "7123456789012345680",
    ]

    try:
        # 批量获取，使用自动模式
        results = await douyin_controller.batch_fetch_video_data(
            aweme_ids=aweme_ids, method="auto", max_concurrent=3, use_proxy=True
        )

        print(f"批量获取完成，共处理 {len(results)} 个视频:")

        success_count = 0
        for i, result in enumerate(results):
            aweme_id = aweme_ids[i]
            if result["success"]:
                success_count += 1
                print(f"✅ {aweme_id}: 成功 (方法: {result.get('method', 'N/A')})")
                print(f"   标题: {result['data'].get('title', 'N/A')[:30]}...")
            else:
                print(f"❌ {aweme_id}: 失败 - {result.get('error', '未知错误')}")

        print(f"\n总结: {success_count}/{len(aweme_ids)} 成功")

    except Exception as e:
        print(f"❌ 批量获取异常: {e}")


async def example_direct_controller_usage():
    """示例：直接使用特定控制器"""
    print("\n" + "=" * 60)
    print("示例：直接使用特定控制器")
    print("=" * 60)

    aweme_id = "7123456789012345678"  # 替换为真实的视频ID

    try:
        # 方式1：直接创建HTML控制器
        html_controller = DouyinHTMLController()

        result = await html_controller.fetch_jingxuan_video_data(aweme_id=aweme_id, use_proxy=True)

        if result["success"]:
            print("✅ 直接使用HTML控制器成功!")
            print(f"   - 数据来源: {result['source']}")
            print(f"   - 请求ID: {result['request_id']}")

        # 方式2：通过主控制器获取子控制器
        video_controller = douyin_controller.get_video_controller()

        # 验证cookies（如果有的话）
        # cookies = "your_cookies_here"
        # validation_result = await video_controller.validate_cookies(cookies)
        # print(f"Cookies验证结果: {validation_result}")

        print("✅ 控制器组件访问成功!")

    except Exception as e:
        print(f"❌ 直接控制器使用异常: {e}")


async def example_configuration_options():
    """示例：配置选项演示"""
    print("\n" + "=" * 60)
    print("示例：配置选项演示")
    print("=" * 60)

    from rpc.douyin.html_handler.config import DouyinHTMLConfig

    # 创建自定义配置
    custom_config = DouyinHTMLConfig(
        request_timeout=30,
        max_retries=5,
        enable_proxy=True,
        enable_cookie_rotation=True,
        enable_anti_crawler_detection=True,
    )

    # 使用自定义配置创建HTML控制器
    html_controller = DouyinHTMLController(config=custom_config)

    aweme_id = "7123456789012345678"  # 替换为真实的视频ID

    try:
        result = await html_controller.fetch_jingxuan_video_data(
            aweme_id=aweme_id,
            use_proxy=False,  # 这个请求特别禁用代理
            custom_headers={
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Cache-Control": "no-cache",
            },
            timeout=25,  # 自定义超时时间
            save_to_db=False,  # 不保存到数据库
        )

        if result["success"]:
            print("✅ 自定义配置请求成功!")
            print(f"   - 响应时间: {result['html_response']['response_time']:.2f}秒")
            print(f"   - 重试次数: {result['html_response']['retry_count']}")
            print(f"   - 最终URL: {result['html_response']['final_url']}")
        else:
            print(f"❌ 自定义配置请求失败: {result.get('error', '未知错误')}")

    except Exception as e:
        print(f"❌ 配置选项演示异常: {e}")


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 抖音控制器使用示例")
    print("=" * 60)

    try:
        # 运行各种示例
        await example_jingxuan_fetch()
        await example_mobile_fetch()
        await example_auto_fetch()
        await example_batch_fetch()
        await example_direct_controller_usage()
        await example_configuration_options()

        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成")

    except KeyboardInterrupt:
        print("\n❌ 用户中断")
    except Exception as e:
        print(f"\n❌ 运行示例时发生错误: {e}")
        logger.exception("示例运行异常")


if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        lambda msg: print(msg, end=""),
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>\n",
    )

    print("⚠️  运行前请确保：")
    print("1. 将示例中的 aweme_id 替换为真实的抖音视频ID")
    print("2. 检查网络连接和代理设置")
    print("3. 确保数据库连接正常（如果需要保存数据）")
    print()

    # 运行示例
    asyncio.run(main())
