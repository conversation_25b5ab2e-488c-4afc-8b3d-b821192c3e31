#!/usr/bin/env python3
"""
简单文件结构验证脚本
验证 controllers/douyin/ 新架构的文件是否正确创建
"""

import os
import sys
from pathlib import Path


def check_file_exists(filepath):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {filepath}")
        return True
    else:
        print(f"❌ {filepath} - 文件不存在")
        return False


def check_file_content(filepath, expected_content=None):
    """检查文件内容"""
    if not os.path.exists(filepath):
        print(f"❌ {filepath} - 文件不存在")
        return False

    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()

        file_size = len(content)
        line_count = content.count("\n")

        print(f"✅ {filepath} - {file_size} 字符, {line_count} 行")

        if expected_content:
            if expected_content in content:
                print(f"   ✅ 包含预期内容: {expected_content[:50]}...")
            else:
                print(f"   ⚠️ 未找到预期内容: {expected_content[:50]}...")

        return True
    except Exception as e:
        print(f"❌ {filepath} - 读取错误: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 检查 controllers/douyin/ 新架构文件结构")
    print("=" * 60)

    # 项目根目录
    project_root = Path(__file__).parent.parent.parent.parent
    controllers_douyin = project_root / "controllers" / "douyin"

    print(f"项目根目录: {project_root}")
    print(f"控制器目录: {controllers_douyin}")
    print()

    # 检查目录存在
    if not controllers_douyin.exists():
        print(f"❌ 目录不存在: {controllers_douyin}")
        return False
    else:
        print(f"✅ 目录存在: {controllers_douyin}")

    print()

    # 检查核心文件
    core_files = [
        ("__init__.py", "DouyinController"),
        ("html_controller.py", "DouyinHTMLController"),
        ("video_controller.py", "DouyinVideoController"),
        ("main_controller.py", "douyin_controller"),
    ]

    print("核心文件检查:")
    print("-" * 30)
    all_core_files_ok = True
    for filename, expected_content in core_files:
        filepath = controllers_douyin / filename
        if not check_file_content(str(filepath), expected_content):
            all_core_files_ok = False

    print()

    # 检查示例文件
    examples_dir = controllers_douyin / "examples"
    example_files = ["quick_test.py", "usage_examples.py", "README.md"]

    print("示例文件检查:")
    print("-" * 30)
    all_example_files_ok = True
    for filename in example_files:
        filepath = examples_dir / filename
        if not check_file_exists(str(filepath)):
            all_example_files_ok = False

    print()

    # 检查原始示例文件
    rpc_examples_dir = project_root / "rpc" / "douyin" / "html_handler" / "examples"
    rpc_example_files = ["fetch_jingxuan_example.py", "quick_test.py"]

    print("RPC示例文件检查:")
    print("-" * 30)
    all_rpc_files_ok = True
    for filename in rpc_example_files:
        filepath = rpc_examples_dir / filename
        if not check_file_exists(str(filepath)):
            all_rpc_files_ok = False

    print()

    # 总结
    print("总结:")
    print("=" * 60)

    if all_core_files_ok:
        print("✅ 核心文件结构完整")
    else:
        print("❌ 核心文件结构有问题")

    if all_example_files_ok:
        print("✅ 控制器示例文件完整")
    else:
        print("❌ 控制器示例文件有问题")

    if all_rpc_files_ok:
        print("✅ RPC示例文件完整")
    else:
        print("❌ RPC示例文件有问题")

    if all_core_files_ok and all_example_files_ok and all_rpc_files_ok:
        print()
        print("🎉 所有文件结构检查通过!")
        print()
        print("📋 新架构说明:")
        print("  - controllers/douyin/__init__.py: 模块导出")
        print("  - controllers/douyin/html_controller.py: HTML方式数据获取")
        print("  - controllers/douyin/video_controller.py: RPC方式数据获取")
        print("  - controllers/douyin/main_controller.py: 统一主控制器")
        print("  - controllers/douyin/examples/: 使用示例和文档")
        print("  - rpc/douyin/html_handler/examples/: fetch_jingxuan_page 专用示例")
        print()
        return True
    else:
        print()
        print("❌ 文件结构检查失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
