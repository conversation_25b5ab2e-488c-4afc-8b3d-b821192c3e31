"""
抖音收藏夹相关控制器

提供抖音收藏夹相关的业务逻辑处理
"""

from typing import Dict, List

from fastapi import HTTPException

from mappers.douyin.collection_mapper import CollectionDataMapper
from models.douyin.models import DouyinAweme
from rpc.douyin.collection_api import async_douyin_collection_api
from rpc.douyin.schemas import (
    CollectVideoListResponse,
    SelfAwemeCollectionResponse,
    VideoInfo,
)
from schemas.douyin import VideoItemResponse
from schemas.trendinsight import DouyinAwemeData


class DouyinCollectionController:
    """抖音收藏夹控制器"""

    def __init__(self):
        pass

    async def _get_douyin_client(self):
        """获取抖音客户端实例"""
        return async_douyin_collection_api

    async def get_self_aweme_collection_rpc_with_cookies(
        self, cursor: int = 0, count: int = 10, cookies: str = None
    ) -> "SelfAwemeCollectionResponse":
        """
        传入cookies获取用户收藏夹列表，返回RPC原响应格式

        Args:
            cursor: 游标，用于分页
            count: 每页数量
            cookies: 抖音网站的Cookie字符串

        Returns:
            SelfAwemeCollectionResponse: RPC原响应格式
        """
        try:
            from rpc.douyin.schemas import SelfAwemeCollectionRequest

            client = await self._get_douyin_client()
            request = SelfAwemeCollectionRequest(cursor=cursor, count=count)

            response = await client.get_self_aweme_collection(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取收藏夹列表失败: {str(e)}")

    async def get_collect_video_list_rpc_with_cookies(
        self, collects_id: str, cursor: int = 0, count: int = 10, cookies: str = None
    ) -> "CollectVideoListResponse":
        """
        传入cookies获取收藏视频列表，返回RPC原响应格式

        Args:
            collects_id: 收藏夹ID
            cursor: 游标，用于分页
            count: 每页数量
            cookies: 抖音网站的Cookie字符串

        Returns:
            CollectVideoListResponse: RPC原响应格式
        """
        try:
            from rpc.douyin.schemas import CollectVideoListRequest

            client = await self._get_douyin_client()
            request = CollectVideoListRequest(collects_id=collects_id, cursor=cursor, count=count)

            response = await client.get_collect_video_list(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取收藏视频列表失败: {str(e)}")

    async def sync_and_save_single_collection_with_cookies(self, collection_id: str, cookies: str) -> Dict:
        """
        同步指定收藏夹并保存数据到数据库（传入cookies）

        该函数会:
        1. 从抖音获取收藏夹内所有视频
        2. 将新视频保存到 douyin_aweme 表

        Args:
            collection_id: 抖音收藏夹ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            Dict: 同步结果统计
        """
        try:
            # 获取抖音客户端
            client = await self._get_douyin_client()

            collections_synced = 0
            videos_synced = 0
            collections_filtered = 1  # 固定为1，因为只处理一个收藏夹
            relations_created = 0
            relations_existing = 0
            trendinsight_relations_created = 0
            trendinsight_relations_existing = 0
            aweme_ids = []
            video_items: List[VideoItemResponse] = []
            errors = []
            
            # 收集所有视频数据用于批量处理
            all_videos: List[VideoInfo] = []

            try:
                # 直接获取指定收藏夹的视频列表
                video_cursor = 0
                video_count = 20

                while True:
                    # 获取收藏夹视频列表
                    from rpc.douyin.schemas import CollectVideoListRequest

                    request = CollectVideoListRequest(collects_id=collection_id, cursor=video_cursor, count=video_count)
                    video_response = await client.get_collect_video_list(request, cookies)

                    if not video_response or not video_response.aweme_list:
                        break

                    # 收集所有视频
                    all_videos.extend(video_response.aweme_list)
                        
                    # 检查是否有更多视频
                    if not video_response.has_more:
                        break

                    video_cursor = video_response.cursor
                
                # 提取所有 aweme_id 并构建 video_items
                aweme_ids = [video.aweme_id for video in all_videos]
                video_items = [
                    VideoItemResponse(
                        aweme_id=video.aweme_id,
                        create_time=video.create_time
                    ) for video in all_videos
                ]
                
                if not all_videos:
                    return {
                        "collections_synced": collections_synced,
                        "videos_synced": videos_synced,
                        "collections_filtered": collections_filtered,
                        "relations_created": relations_created,
                        "relations_existing": relations_existing,
                        "trendinsight_relations_created": trendinsight_relations_created,
                        "trendinsight_relations_existing": trendinsight_relations_existing,
                        "aweme_ids": aweme_ids,
                        "video_items": video_items,
                        "errors": errors,
                    }
                
                # 批量查询已存在的视频
                existing_videos = await DouyinAweme.filter(aweme_id__in=aweme_ids).all()
                existing_video_ids = {video.aweme_id for video in existing_videos}
                
                # 准备要创建的视频数据
                videos_to_create = []
                for video in all_videos:
                    # 如果视频不存在，则准备创建
                    if video.aweme_id not in existing_video_ids:
                        # 使用mapper进行数据转换
                        douyin_aweme_data = CollectionDataMapper.map_video_info_to_douyin_aweme(video)
                        # 将 Pydantic 模型转换为字典
                        aweme_dict = douyin_aweme_data.model_dump()
                        # 创建 Tortoise ORM 模型实例
                        douyin_aweme = DouyinAweme(**aweme_dict)
                        videos_to_create.append(douyin_aweme)

                # 批量创建新视频
                if videos_to_create:
                    await DouyinAweme.bulk_create(videos_to_create)
                    videos_synced = len(videos_to_create)

                # 如果成功处理了视频，则认为收藏夹同步成功
                if videos_synced > 0:
                    collections_synced = 1

            except Exception as collection_error:
                errors.append(f"处理收藏夹 {collection_id} 时出错: {str(collection_error)}")

            return {
                "collections_synced": collections_synced,
                "videos_synced": videos_synced,
                "collections_filtered": collections_filtered,
                "relations_created": relations_created,
                "relations_existing": relations_existing,
                "trendinsight_relations_created": trendinsight_relations_created,
                "trendinsight_relations_existing": trendinsight_relations_existing,
                "aweme_ids": aweme_ids,
                "video_items": video_items,
                "errors": errors,
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"同步收藏夹失败: {str(e)}")


# 创建控制器实例
douyin_collection_controller = DouyinCollectionController()

# 保持向后兼容性的别名
collection_controller = douyin_collection_controller