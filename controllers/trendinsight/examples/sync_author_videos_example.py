#!/usr/bin/env python3
"""
TrendInsight 作者视频同步示例

展示如何使用 sync_author_videos 方法同步作者和其视频列表
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from controllers.trendinsight.author_sync_controller import AuthorSyncController
from models.enums import AuthorActionType
from tortoise import Tortoise
from settings.config import settings


async def sync_author_videos_example():
    """作者视频同步示例"""
    
    print("🎬 TrendInsight 作者视频同步示例")
    print("=" * 60)
    
    # 初始化控制器
    controller = AuthorSyncController()
    
    # 示例用户ID（请替换为真实的 TrendInsight 用户ID）
    user_id = "ccgijibiifjddcef"
    
    try:
        print(f"📋 开始同步作者: {user_id}")
        print("🚀 正在执行同步操作...")
        print()
        
        # 调用同步方法
        result = await controller.sync_author_videos(user_id)
        
        print("✅ 同步操作完成！")
        print("📊 同步结果统计:")
        print(f"  - 作者操作类型: {result.author_action}")
        print(f"  - 视频同步数量: {result.videos_synced}")
        print(f"  - 视频失败数量: {result.videos_failed}")
        print(f"  - 新建关联记录: {result.relations_created}")
        print(f"  - 已存在关联: {result.relations_existing}")
        print(f"  - 获取视频ID数: {len(result.aweme_ids)}")
        print(f"  - 视频详情数量: {len(result.video_items)}")
        print()
        
        # 显示作者信息
        if result.author_data:
            print("👤 作者信息:")
            print(f"  - 用户ID: {result.author_data.user_id}")
            print(f"  - 用户名: {result.author_data.user_name}")
            print(f"  - 作品数量: {result.author_data.item_count}")
            print(f"  - 粉丝数量: {result.author_data.fans_count}")
            print(f"  - 点赞数量: {result.author_data.like_count}")
            if hasattr(result.author_data, 'user_location') and result.author_data.user_location:
                print(f"  - 用户位置: {result.author_data.user_location}")
            print()
        
        # 显示视频ID列表（前10个）
        if result.aweme_ids:
            print("🎥 获取到的视频ID列表（前10个）:")
            for i, aweme_id in enumerate(result.aweme_ids[:10], 1):
                print(f"  {i:2d}. {aweme_id}")
            if len(result.aweme_ids) > 10:
                print(f"  ... 还有 {len(result.aweme_ids) - 10} 个视频")
            print()
        
        # 显示视频详情（前3个）
        if result.video_items:
            print("📹 视频详情示例（前3个）:")
            print("-" * 50)
            for i, video in enumerate(result.video_items[:3], 1):
                print(f"  视频 {i}:")
                print(f"    ID: {video.aweme_id}")
                print(f"    标题: {video.title}")
                print(f"    描述: {video.desc[:50]}..." if len(video.desc) > 50 else f"    描述: {video.desc}")
                print(f"    作者: {video.nickname}")
                print(f"    点赞数: {video.liked_count}")
                print(f"    评论数: {video.comment_count}")
                print(f"    分享数: {video.share_count}")
                print(f"    收藏数: {video.collected_count}")
                print(f"    来源关键词: {video.source_keyword}")
                print()
        
        # 显示错误信息
        if result.errors:
            print("⚠️  错误信息:")
            for i, error in enumerate(result.errors, 1):
                print(f"  {i}. {error}")
            print()
        
        # 根据作者操作类型显示不同信息
        if result.author_action == AuthorActionType.CREATED:
            print("🆕 这是一个新作者，已成功创建作者记录")
        elif result.author_action == AuthorActionType.EXISTING:
            print("👥 作者已存在，使用现有记录进行同步")
        
        return result
        
    except Exception as e:
        print(f"❌ 同步失败: {str(e)}")
        print(f"🔍 错误类型: {type(e).__name__}")
        return None


async def sync_multiple_authors_example():
    """批量同步多个作者示例"""
    
    print("\n" + "=" * 60)
    print("🎬 批量作者同步示例")
    print("=" * 60)
    
    # 示例用户ID列表（请替换为真实的 TrendInsight 用户ID）
    user_ids = [
        "heicgcbajggjdjjaefj",
        "example_user_id_2", 
        "example_user_id_3"
    ]
    
    controller = AuthorSyncController()
    results = []
    
    for i, user_id in enumerate(user_ids, 1):
        print(f"\n📋 正在同步第 {i}/{len(user_ids)} 个作者: {user_id}")
        
        try:
            result = await controller.sync_author_videos(user_id)
            results.append({
                "user_id": user_id,
                "success": True,
                "result": result
            })
            print(f"✅ 作者 {user_id} 同步成功")
            print(f"   - 同步视频: {result.videos_synced} 个")
            print(f"   - 新建关联: {result.relations_created} 个")
            
        except Exception as e:
            results.append({
                "user_id": user_id,
                "success": False,
                "error": str(e)
            })
            print(f"❌ 作者 {user_id} 同步失败: {str(e)}")
        
        # 添加延迟避免请求过快
        if i < len(user_ids):
            await asyncio.sleep(2)
    
    # 统计结果
    successful = sum(1 for r in results if r["success"])
    failed = len(results) - successful
    total_videos = sum(r["result"].videos_synced for r in results if r["success"])
    total_relations = sum(r["result"].relations_created for r in results if r["success"])
    
    print(f"\n📊 批量同步结果统计:")
    print(f"  - 成功同步: {successful}/{len(user_ids)} 个作者")
    print(f"  - 失败数量: {failed} 个作者")
    print(f"  - 总同步视频: {total_videos} 个")
    print(f"  - 总新建关联: {total_relations} 个")
    
    return results


async def demonstrate_api_usage():
    """演示 API 调用方式"""
    
    print("\n" + "=" * 60)
    print("🌐 API 调用示例")
    print("=" * 60)
    
    print("📡 HTTP API 调用示例:")
    print()
    
    # cURL 示例
    curl_example = '''curl -X POST "http://localhost:8000/api/v1/trendinsight/author/sync" \\
     -H "Content-Type: application/json" \\
     -d '{"user_id": "heicgcbajggjdjjaefj"}'
'''
    
    print("🔧 cURL 命令:")
    print(curl_example)
    
    # Python requests 示例
    python_example = '''import requests

# 同步作者视频
response = requests.post(
    "http://localhost:8000/api/v1/trendinsight/author/sync",
    json={"user_id": "heicgcbajggjdjjaefj"}
)

if response.status_code == 200:
    result = response.json()
    print(f"同步成功，获取到 {result['videos_synced']} 个视频")
    print(f"作者操作类型: {result['author_action']}")
else:
    print(f"同步失败: {response.text}")
'''
    
    print("🐍 Python requests 示例:")
    print(python_example)
    
    # JavaScript fetch 示例
    js_example = '''// JavaScript fetch 示例
fetch('http://localhost:8000/api/v1/trendinsight/author/sync', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        user_id: 'heicgcbajggjdjjaefj'
    })
})
.then(response => response.json())
.then(data => {
    console.log('同步成功:', data);
    console.log(`获取到 ${data.videos_synced} 个视频`);
})
.catch(error => {
    console.error('同步失败:', error);
});
'''
    
    print("🌐 JavaScript fetch 示例:")
    print(js_example)


async def main():
    """主函数"""

    print("🎯 TrendInsight 作者视频同步完整示例")
    print("=" * 60)
    print()
    print("本示例将演示以下功能:")
    print("1. 单个作者视频同步")
    print("2. 批量作者同步")
    print("3. API 调用方式")
    print()

    # 初始化数据库连接
    print("🔧 正在初始化数据库连接...")
    try:
        await Tortoise.init(config=settings.tortoise_orm)
        print("✅ 数据库连接初始化成功")
    except Exception as e:
        print(f"❌ 数据库连接初始化失败: {str(e)}")
        return

    try:
        # 运行单个作者同步示例
        await sync_author_videos_example()

        # 运行批量同步示例（可选，取消注释以启用）
        # await sync_multiple_authors_example()

        # 显示 API 使用示例
        await demonstrate_api_usage()

        print("\n" + "=" * 60)
        print("✨ 示例运行完成！")
        print()
        print("💡 提示:")
        print("- 请确保数据库中有有效的 TrendInsight cookies")
        print("- 请确保数据库中有有效的抖音平台 cookies")
        print("- 请将示例中的 user_id 替换为真实的 TrendInsight 用户ID")
        print("- 系统会自动处理重复数据，不会创建重复记录")

    finally:
        # 关闭数据库连接
        print("\n🔧 正在关闭数据库连接...")
        await Tortoise.close_connections()
        print("✅ 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
