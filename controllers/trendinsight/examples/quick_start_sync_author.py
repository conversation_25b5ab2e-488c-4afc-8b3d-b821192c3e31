#!/usr/bin/env python3
"""
TrendInsight 作者同步快速开始示例

最简单的作者视频同步示例，适合快速测试和学习
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from controllers.trendinsight.author_sync_controller import AuthorSyncController
from tortoise import Tortoise
from settings.config import settings


async def quick_sync_example():
    """快速同步示例"""
    
    print("🚀 TrendInsight 作者同步 - 快速开始")
    print("=" * 50)
    
    # 1. 创建控制器实例
    controller = AuthorSyncController()
    
    # 2. 设置要同步的用户ID（请替换为真实的 TrendInsight 用户ID）
    user_id = "heicgcbajggjdjjaefj"
    
    try:
        # 3. 执行同步
        print(f"📋 开始同步作者: {user_id}")
        result = await controller.sync_author_videos(user_id)
        
        # 4. 显示结果
        print("✅ 同步完成！")
        print(f"📊 结果: 同步了 {result.videos_synced} 个视频")
        print(f"🔗 创建了 {result.relations_created} 个新关联")
        print(f"👤 作者状态: {result.author_action}")
        
        if result.author_data:
            print(f"📝 作者名称: {result.author_data.user_name}")
            print(f"👥 粉丝数量: {result.author_data.fans_count}")
        
        return result
        
    except Exception as e:
        print(f"❌ 同步失败: {str(e)}")
        return None


async def main():
    """主函数"""

    print("💡 使用前请确保:")
    print("- 数据库中有有效的 TrendInsight cookies")
    print("- 数据库中有有效的抖音平台 cookies")
    print("- 将 user_id 替换为真实的 TrendInsight 用户ID")
    print()

    # 初始化数据库连接
    print("🔧 正在初始化数据库连接...")
    try:
        await Tortoise.init(config=settings.tortoise_orm)
        print("✅ 数据库连接初始化成功")
    except Exception as e:
        print(f"❌ 数据库连接初始化失败: {str(e)}")
        return

    try:
        # 执行同步
        result = await quick_sync_example()

        if result:
            print("\n🎉 同步成功完成！")
            print(f"📈 获取到的视频ID数量: {len(result.aweme_ids)}")

            # 显示前5个视频ID
            if result.aweme_ids:
                print("🎥 前5个视频ID:")
                for i, aweme_id in enumerate(result.aweme_ids[:5], 1):
                    print(f"  {i}. {aweme_id}")
        else:
            print("\n💔 同步失败，请检查配置和网络连接")

    finally:
        # 关闭数据库连接
        print("\n🔧 正在关闭数据库连接...")
        await Tortoise.close_connections()
        print("✅ 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
