#!/usr/bin/env python3
"""
测试数据库连接示例

验证数据库连接是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from tortoise import Tortoise
from settings.config import settings


async def test_db_connection():
    """测试数据库连接"""
    
    print("🔧 测试数据库连接...")
    
    try:
        # 初始化数据库连接
        await Tortoise.init(config=settings.tortoise_orm)
        print("✅ 数据库连接成功")
        
        # 测试查询一个简单的表
        from models.trendinsight.models import TrendInsightAuthor
        
        # 获取作者数量
        author_count = await TrendInsightAuthor.all().count()
        print(f"📊 数据库中共有 {author_count} 个作者记录")
        
        # 如果有作者记录，显示第一个
        if author_count > 0:
            first_author = await TrendInsightAuthor.first()
            print(f"👤 第一个作者: {first_author.user_name} (ID: {first_author.user_id})")
        else:
            print("📭 数据库中暂无作者记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        print(f"🔍 错误类型: {type(e).__name__}")
        return False
    
    finally:
        # 关闭数据库连接
        try:
            await Tortoise.close_connections()
            print("🔧 数据库连接已关闭")
        except Exception as e:
            print(f"⚠️  关闭数据库连接时出错: {str(e)}")


async def main():
    """主函数"""
    
    print("🧪 数据库连接测试")
    print("=" * 40)
    
    success = await test_db_connection()
    
    if success:
        print("\n🎉 数据库连接测试成功！")
        print("💡 现在可以运行作者同步示例了")
    else:
        print("\n💔 数据库连接测试失败")
        print("💡 请检查:")
        print("  - 数据库文件是否存在")
        print("  - 数据库配置是否正确")
        print("  - 是否已运行数据库迁移")


if __name__ == "__main__":
    asyncio.run(main())
