# TrendInsight 控制器模块

本目录包含 TrendInsight 平台相关的控制器，已经拆分为以下几个专门的控制器：

## 模块结构

### 1. `main_controller.py` - 主控制器
负责基础的 TrendInsight API 调用功能：
- 用户信息查询 (`query_user_self_info`)
- 达人搜索 (`query_daren_sug_great_user_list`)
- 关键词搜索视频 (`search_info_by_keyword`)
- 获取作者详情 (`get_author_detail`)
- 获取视频指数数据 (`get_video_index`)
- 接口可用性测试 (`pong`)

### 2. `author_sync_controller.py` - 作者同步控制器
负责作者和其视频列表的同步功能：
- 同步作者和其视频列表 (`sync_author_videos`)
- 获取作者详情 (`get_author_detail`)

### 3. `keyword_sync_controller.py` - 关键词同步控制器
负责关键词相关视频列表的同步功能：
- 同步关键词相关视频 (`sync_keyword_videos`)
- 关键词搜索视频 (`search_info_by_keyword`)
- 计算关键词哈希 (`_calculate_keyword_hash`)

### 4. `video_process_controller.py` - 视频处理控制器
负责视频ID处理和视频详情获取的功能：
- 处理视频ID (`process_video_id`)
- 批量获取缺失视频详情 (`fetch_missing_video_details`)
- 批量获取视频趋势评分 (`fetch_video_trend_scores`)
- 单个视频详情获取 (`_fetch_single_video_detail`)
- 视频数据格式转换 (`_convert_video_info_to_aweme_data`)

## 向后兼容性

原来的 `controllers/trendinsight.py` 文件现在作为向后兼容的包装器，通过委托模式调用各个专门的控制器，确保现有代码无需修改。

## 使用方式

### 直接使用拆分后的控制器
```python
from controllers.trendinsight.main_controller import TrendInsightMainController
from controllers.trendinsight.author_sync_controller import AuthorSyncController
from controllers.trendinsight.keyword_sync_controller import KeywordSyncController
from controllers.trendinsight.video_process_controller import VideoProcessController

# 创建控制器实例
main_controller = TrendInsightMainController()
author_sync = AuthorSyncController()
keyword_sync = KeywordSyncController()
video_process = VideoProcessController()
```

### 使用向后兼容的包装器（已废弃，推荐直接使用专门控制器）
```python
# 旧的使用方式（现已不可用）
# from controllers.trendinsight import trendinsight_controller
# result = await trendinsight_controller.sync_author_videos(user_id)

# 新的推荐使用方式
from controllers.trendinsight.author_sync_controller import author_sync_controller
result = await author_sync_controller.sync_author_videos(user_id)
```

### 通过模块导入
```python
from controllers.trendinsight import (
    AuthorSyncController,
    KeywordSyncController,
    VideoProcessController,
    TrendInsightMainController,
)
```

## RPC 集成

所有控制器都使用内置的账号提供者（Account Provider）来处理认证：
- `async_douyin_api`: 抖音 RPC 客户端，内置账号管理
- `client_manager.create_async_client()`: TrendInsight 客户端管理器

不再需要手动处理 cookies，账号提供者会自动管理认证状态。