"""
TrendInsight 作者同步控制器

负责同步作者和其视频列表相关的业务逻辑
"""

import logging
from typing import List

from fastapi import HTTPException

from mappers.trendinsight import TrendInsightVideoMapper

from models.enums import AuthorActionType, Platform, SourceType
from models.trendinsight.models import (
    TrendInsightAuthor,
    TrendInsightVideoRelated,
    update_trendinsight_author,
)
from rpc.trendinsight import AsyncTrendInsightAPI, client_manager
from rpc.trendinsight.config import TrendInsightConfig
from rpc.trendinsight.schemas import AuthorDetailResponse, GreatUserTopVideoRequest
from schemas.trendinsight import AuthorData, AuthorSyncResponse, AuthorUpdateData


class AuthorSyncController:
    """TrendInsight 作者同步控制器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = TrendInsightConfig()

    async def _get_trendinsight_client(self) -> AsyncTrendInsightAPI:
        """
        获取 TrendInsight 异步客户端实例

        Returns:
            AsyncTrendInsightAPI: 配置好账号提供者的异步客户端实例
        """
        # 通过 client_manager 创建客户端，cookies 由账号提供者自动管理
        async_client = client_manager.create_async_client()
        return AsyncTrendInsightAPI(async_client=async_client)

    async def get_author_detail(self, user_id: str) -> AuthorDetailResponse:
        """
        获取作者详情接口

        Args:
            user_id: 用户ID

        Returns:
            AuthorDetailResponse: 作者详情响应
        """
        try:
            client = await self._get_trendinsight_client()
            response = await client.get_author_detail(user_id)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取作者详情失败: {str(e)}")

    async def sync_author_videos(self, user_id: str) -> AuthorSyncResponse:
        """
        同步作者和其视频列表

        功能：
        1. 通过 TrendInsight API 获取作者详情，不存在则创建，存在则使用现有记录
        2. 通过 TrendInsight API 获取用户热门视频列表（最近30天）
        3. 在 trendinsight_video_related 表中创建关联记录（不存在才创建）
        4. 智能批量处理 DouyinAweme 表记录（创建和更新）

        Args:
            user_id: TrendInsight 用户ID

        Returns:
            AuthorSyncResponse: 同步结果信息

        Example:
            {
                "author_action": "created",
                "author_data": {
                    "user_id": "heicgcbajggjdjjaefj",
                    "user_name": "科技博主",
                    "douyin_user_id": "MS4wLjABAAAA123",
                },
                "videos_synced": 15,
                "videos_failed": 0,
                "relations_created": 15,
                "relations_existing": 0,
                "aweme_ids": ["7123456789012345678", "7123456789012345679"],
                "video_items": [
                    {
                        "aweme_id": "7123456789012345678",
                        "title": "科技前沿视频",
                        "desc": "这是一个关于科技前沿的视频",
                        "create_time": 1705314645,
                        "nickname": "科技博主",
                        "liked_count": "1000",
                        "comment_count": "50",
                        "share_count": "20",
                        "collected_count": "30",
                        "source_keyword": "author_sync_heicgcbajggjdjjaefj"
                    }
                ],
                "errors": []
            }
        """
        try:
            # 初始化 AuthorSyncResponse 实例，提供类型安全性
            sync_result = AuthorSyncResponse(
                author_action=AuthorActionType.EXISTING,  # 默认值，后续会更新
                author_data=None,
                videos_synced=0,
                videos_failed=0,
                relations_created=0,
                relations_existing=0,
                aweme_ids=[],
                video_items=[],
                errors=[],
            )

            # 1. 检查作者是否已存在
            existing_author = await TrendInsightAuthor.filter(user_id=user_id).first()

            if existing_author:
                sync_result.author_action = AuthorActionType.EXISTING
                sync_result.author_data = AuthorData.model_validate(existing_author)
                print(f"作者已存在: {user_id}")
            else:
                # 2. 通过 TrendInsight API 获取作者详情
                try:
                    print(f"开始获取作者详情: {user_id}")
                    author_detail = await self.get_author_detail(user_id)

                    if author_detail.is_success and author_detail.author_detail:
                        author_data = author_detail.author_detail

                        # 3. 转换并保存作者数据 - 使用强类型数据模型
                        author_update_data = AuthorUpdateData.from_author_detail(
                            author_detail=author_data,
                            user_id=user_id
                        )

                        # 作者创建时将 item_count 和 item_count_int 初始化为 0
                        author_dict = author_update_data.to_dict()
                        author_dict["item_count"] = "0"  # 初始化为 0，后续会根据实际同步结果更新

                        success = await update_trendinsight_author(author_dict)
                        if success:
                            sync_result.author_action = AuthorActionType.CREATED
                            # 获取刚创建的作者记录
                            created_author = await TrendInsightAuthor.filter(user_id=user_id).first()
                            if created_author:
                                sync_result.author_data = AuthorData.model_validate(created_author)
                            print(f"作者创建成功: {user_id}")
                        else:
                            error_msg: str = f"保存作者数据失败: {user_id}"
                            sync_result.errors.append(error_msg)
                            print(error_msg)
                            return sync_result
                    else:
                        error_msg: str = f"获取作者详情失败: {user_id}"
                        sync_result.errors.append(error_msg)
                        print(error_msg)
                        return sync_result

                except Exception as e:
                    error_msg: str = f"获取或创建作者失败: {str(e)}"
                    sync_result.errors.append(error_msg)
                    print(error_msg)
                    return sync_result

            # 4. 通过 TrendInsight RPC 服务获取用户热门视频列表
            try:
                # 使用 TrendInsight API 获取用户热门视频
                trendinsight_client = await self._get_trendinsight_client()

                # 构建日期范围（获取最近30天的视频）
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)

                # 构建 TrendInsight 用户热门视频请求
                video_request = GreatUserTopVideoRequest(
                    user_id=user_id,  # 使用 TrendInsight 用户ID
                    start_date=start_date.strftime("%Y%m%d"),
                    end_date=end_date.strftime("%Y%m%d")
                )

                video_response = await trendinsight_client.get_great_user_top_video(video_request)

                if video_response.is_success and video_response.video_data:
                    # 6. 批量检查和创建视频关联记录
                    video_data = video_response.video_data

                    # 只取指数排序的视频列表（内容都是一样的，取一个就够了）
                    video_list = video_data.index_list or []

                    # 使用映射器将 TrendInsight TopVideoInfo 数据转换为完整的 DouyinAwemeData 格式
                    video_data_list, video_ids = TrendInsightVideoMapper.top_video_list_to_douyin_aweme_data_list(
                        video_list=video_list, source_keyword=f"author_sync_{user_id}"
                    )

                    # 将完整的视频数据赋值给响应（符合类型注解 List[DouyinAwemeData]）
                    sync_result.video_items = video_data_list
                    sync_result.aweme_ids = video_ids  # 保持向后兼容

                    # 智能批量处理 DouyinAweme 表记录（创建和更新）
                    try:
                        print(f"开始智能处理 DouyinAweme 记录，作者: {user_id}")

                        # 使用服务层处理数据库操作，传入作者信息以补充视频数据
                        from controllers.trendinsight.services import DouyinAwemeService
                        aweme_created, aweme_existing = await DouyinAwemeService.ensure_douyin_aweme_records(
                            video_data_list=video_data_list,
                            video_ids=video_ids,
                            author_data=sync_result.author_data  # 传入作者信息
                        )
                        print(f"DouyinAweme 智能处理完成 - 新创建: {aweme_created}, 已存在/更新: {aweme_existing}")

                        # 更新同步结果统计
                        sync_result.videos_synced = len(video_ids)

                    except Exception as e:
                        error_msg: str = f"DouyinAweme 表操作失败: {str(e)}"
                        sync_result.errors.append(error_msg)
                        print(error_msg)
                        # 继续执行，不中断主流程

                    # 批量检查已存在的关联记录
                    existing_relations = await TrendInsightVideoRelated.filter(
                        source_id=user_id, video_id__in=video_ids
                    ).values_list("video_id", flat=True)

                    existing_video_ids = set(existing_relations)
                    sync_result.relations_existing = len(existing_video_ids)

                    # 准备批量创建的数据
                    new_relations: List[TrendInsightVideoRelated] = []
                    for video_id in video_ids:
                        if video_id not in existing_video_ids:
                            new_relation = TrendInsightVideoRelated(
                                source_type=SourceType.AUTHOR,
                                source_id=user_id,
                                video_id=video_id,
                                platform=Platform.DOUYIN,
                            )
                            new_relations.append(new_relation)

                    # 批量创建新关联记录
                    if new_relations:
                        await TrendInsightVideoRelated.bulk_create(new_relations)
                        sync_result.relations_created = len(new_relations)
                        print(f"创建了 {len(new_relations)} 个新的作者视频关联记录")
                    else:
                        sync_result.relations_created = 0
                        print(f"所有作者视频关联已存在，无需创建: {user_id}")
                else:
                    error_msg: str = f"获取 TrendInsight 用户热门视频失败: status={video_response.status}, msg={video_response.msg}"
                    sync_result.errors.append(error_msg)
                    print(error_msg)

            except Exception as e:
                error_msg: str = f"同步用户热门视频失败: {str(e)}"
                sync_result.errors.append(error_msg)
                print(error_msg)

            # 5. 同步完成后更新作者的实际视频计数
            try:
                # 查询 trendinsight_video_related 表，统计该作者关联的实际视频数量
                # TrendInsightVideoRelated 模型没有 platform 字段，只使用 source_type 和 source_id 过滤
                actual_video_count = await TrendInsightVideoRelated.filter(
                    source_type=SourceType.AUTHOR,
                    source_id=user_id
                ).count()

                print(f"作者 {user_id} 实际关联视频数量: {actual_video_count}")

                # 更新作者记录的 item_count 字段为实际同步成功的数量
                if sync_result.author_data:
                    author_record = await TrendInsightAuthor.filter(user_id=user_id).first()
                    if author_record:
                        # 更新 item_count 为实际数量
                        author_record.item_count = str(actual_video_count)
                        author_record.item_count_int = actual_video_count
                        await author_record.save()

                        # 更新响应中的作者数据
                        sync_result.author_data.item_count = str(actual_video_count)

                        print(f"已更新作者 {user_id} 的 item_count 为实际数量: {actual_video_count}")
                    else:
                        print(f"警告: 未找到作者记录进行计数更新: {user_id}")

            except Exception as e:
                error_msg: str = f"更新作者视频计数失败: {str(e)}"
                sync_result.errors.append(error_msg)
                print(error_msg)
                # 不中断主流程，继续返回结果

            return sync_result

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"同步作者和视频失败: {str(e)}")


# 创建控制器实例
author_sync_controller = AuthorSyncController()
